# 日期工具函数
# 提供日期范围处理等功能

from datetime import datetime, timedelta


def get_date_range(time_dimension='daily', start_date=None, end_date=None, default_days=30):
    """
    获取日期范围
    支持日/周/月/季/年维度的日期范围处理
    
    参数:
        time_dimension: 时间维度，可选值：daily, weekly, monthly, quarterly, yearly
        start_date: 开始日期，格式：YYYY-MM-DD
        end_date: 结束日期，格式：YYYY-MM-DD
        default_days: 默认天数，当未提供开始日期时使用
    
    返回:
        start_date, end_date: 处理后的开始日期和结束日期
    """
    # 处理结束日期
    if not end_date:
        end_date = datetime.now()
    elif isinstance(end_date, str):
        end_date = datetime.strptime(end_date, '%Y-%m-%d')
    
    # 处理开始日期
    if not start_date:
        # 根据时间维度设置默认的开始日期
        if time_dimension == 'daily':
            start_date = end_date - timedelta(days=default_days)
        elif time_dimension == 'weekly':
            start_date = end_date - timedelta(weeks=default_days // 7 or 4)
        elif time_dimension == 'monthly':
            # 默认取最近N个月
            start_date = end_date - timedelta(days=default_days)
        elif time_dimension == 'quarterly':
            # 默认取最近4个季度
            start_date = end_date - timedelta(days=365)
        elif time_dimension == 'yearly':
            # 默认取最近3年
            start_date = end_date - timedelta(days=365 * 3)
        else:
            start_date = end_date - timedelta(days=default_days)
    elif isinstance(start_date, str):
        start_date = datetime.strptime(start_date, '%Y-%m-%d')
    
    return start_date, end_date