# 认证中间件

import re
from django.http import JsonResponse
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.conf import settings


class AuthenticationMiddleware:
    """
    认证中间件
    用于拦截所有请求，除了登录接口外，其他接口都需要认证
    """
    def __init__(self, get_response):
        self.get_response = get_response
        # 不需要认证的URL列表
        self.exempt_urls = [
            r'^/api/analytics/users/auth/login/',  # 登录接口
            r'^/api/analytics/users/auth/token/refresh/',  # 刷新令牌接口
            r'^/admin/',  # Django管理后台
            r'^/static/',  # 静态文件
        ]
        # 编译正则表达式
        self.exempt_urls = [re.compile(url) for url in self.exempt_urls]
        # JWT认证器
        self.jwt_auth = JWTAuthentication()

    def __call__(self, request):
        # 检查是否是豁免的URL
        path = request.path_info
        if any(pattern.match(path) for pattern in self.exempt_urls):
            return self.get_response(request)

        # 检查认证头
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if not auth_header.startswith('Bearer '):
            return JsonResponse({
                'detail': '身份认证信息未提供或格式不正确。请提供有效的Bearer令牌。'
            }, status=401)

        # 验证令牌
        try:
            token = auth_header.split(' ')[1]
            validated_token = self.jwt_auth.get_validated_token(token)
            user = self.jwt_auth.get_user(validated_token)
            request.user = user
        except Exception as e:
            return JsonResponse({
                'detail': f'令牌无效或已过期: {str(e)}'
            }, status=401)

        # 继续处理请求
        return self.get_response(request)
