import os
import logging
from logging.handlers import RotatingFile<PERSON>and<PERSON>, TimedRotatingFileHandler
from pathlib import Path

# 获取项目根目录
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# 确保logs目录存在
LOGS_DIR = os.path.join(BASE_DIR, 'logs')
os.makedirs(LOGS_DIR, exist_ok=True)

def configure_logging(service_type='web'):
    """
    配置日志系统
    - 将日志保存到logs目录下
    - 同时输出到控制台
    - 按日期和大小进行日志轮转

    参数:
    - service_type: 服务类型，可以是'web'或'task'，默认为'web'
    """
    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # 清除现有的处理器，避免重复配置
    if root_logger.handlers:
        for handler in root_logger.handlers:
            root_logger.removeHandler(handler)

    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # 根据服务类型选择日志文件名
    if service_type == 'task':
        app_log_filename = 'app-task.log'
        error_log_filename = 'error-task.log'
    else:  # 'web'或其他
        app_log_filename = 'app.log'
        error_log_filename = 'error.log'

    # 创建按时间轮转的文件处理器（每天一个日志文件）
    daily_handler = TimedRotatingFileHandler(
        filename=os.path.join(LOGS_DIR, app_log_filename),
        when='midnight',
        interval=1,
        backupCount=30,  # 保留30天的日志
        encoding='utf-8'
    )
    daily_handler.setLevel(logging.INFO)
    daily_handler.setFormatter(formatter)

    # 创建按大小轮转的错误日志处理器
    error_handler = RotatingFileHandler(
        filename=os.path.join(LOGS_DIR, error_log_filename),
        maxBytes=10*1024*1024,  # 10MB
        backupCount=10,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(formatter)

    # 添加处理器到根日志记录器
    root_logger.addHandler(console_handler)
    root_logger.addHandler(daily_handler)
    root_logger.addHandler(error_handler)

    # 配置Django日志
    django_logger = logging.getLogger('django')
    django_logger.setLevel(logging.INFO)

    # 配置任务模块日志
    tasks_logger = logging.getLogger('src.apps.tasks')
    tasks_logger.setLevel(logging.INFO)

    # 配置数据库日志
    db_logger = logging.getLogger('django.db.backends')
    db_logger.setLevel(logging.WARNING)  # 只记录警告及以上级别

    # 配置Elasticsearch日志
    es_logger = logging.getLogger('elasticsearch')
    es_logger.setLevel(logging.WARNING)  # 只记录警告及以上级别

    logging.info("日志系统已配置，日志文件保存在 %s 目录下", LOGS_DIR)
