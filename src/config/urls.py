# Django项目URL配置

from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    # API路由
    path('api/analytics/users/', include('src.apps.users.urls')),  # 移动到analytics下
    path('api/features/', include('src.apps.features.urls')),
    path('api/analytics/', include('src.apps.analytics.urls')),
    path('api/reports/', include('src.apps.reports.urls')),
    path('api/tasks/', include('src.apps.tasks.urls')),
]