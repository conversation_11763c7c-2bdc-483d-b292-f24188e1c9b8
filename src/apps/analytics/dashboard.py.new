# 首页仪表盘视图

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q
from datetime import datetime, timedelta

from .models import UserOperateAnalysisDay
from .serializers import UserOperateAnalysisDaySerializer

from src.utils.dashboard_utils import (
    get_active_users,
    get_function_calls,
    get_avg_response_time,
    get_error_rate,
    get_tps_and_response_time,
    get_interface_tps_and_records,
    get_operation_logs,
    get_feature_operation_analysis
)


class DashboardViewSet(viewsets.ViewSet):
    """
    首页仪表盘视图集
    提供首页实时概况数据
    """

    @action(detail=False, methods=['get'])
    def overview(self, request):
        """
        获取首页概况数据
        包括：活跃用户数、功能调用次数、平均响应时间、错误率
        """
        # 获取时间范围参数（小时）
        time_range_param = request.query_params.get('time_range')
        # 如果提供了time_range参数，则使用该参数，否则传入None，让utils函数使用默认的当天0点到当前时间
        time_range = int(time_range_param) if time_range_param is not None else None

        try:
            # 获取四个实时指标数据
            active_users = get_active_users(time_range)
            function_calls = get_function_calls(time_range)
            avg_response_time = get_avg_response_time(time_range)
            error_rate = get_error_rate(time_range)

            # 返回结果，如果time_range为None，则表示使用的是当天0点到当前时间
            result = {
                'active_users': active_users,
                'function_calls': function_calls,
                'avg_response_time': avg_response_time,
                'error_rate': error_rate,
                'time_range': '当天' if time_range is None else time_range
            }

            return Response(result)

        except Exception as e:
            return Response(
                {'error': f'获取首页概况数据失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def active_users(self, request):
        """
        获取活跃用户数
        """
        time_range_param = request.query_params.get('time_range')
        # 如果提供了time_range参数，则使用该参数，否则传入None，让utils函数使用默认的当天0点到当前时间
        time_range = int(time_range_param) if time_range_param is not None else None

        try:
            result = get_active_users(time_range)
            # 添加time_range信息到返回结果
            result['time_range'] = '当天' if time_range is None else time_range
            return Response(result)
        except Exception as e:
            return Response(
                {'error': f'获取活跃用户数失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def function_calls(self, request):
        """
        获取功能调用次数
        """
        time_range_param = request.query_params.get('time_range')
        # 如果提供了time_range参数，则使用该参数，否则传入None，让utils函数使用默认的当天0点到当前时间
        time_range = int(time_range_param) if time_range_param is not None else None

        try:
            result = get_function_calls(time_range)
            # 添加time_range信息到返回结果
            result['time_range'] = '当天' if time_range is None else time_range
            return Response(result)
        except Exception as e:
            return Response(
                {'error': f'获取功能调用次数失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def avg_response_time(self, request):
        """
        获取平均响应时间
        """
        time_range_param = request.query_params.get('time_range')
        # 如果提供了time_range参数，则使用该参数，否则传入None，让utils函数使用默认的当天0点到当前时间
        time_range = int(time_range_param) if time_range_param is not None else None

        try:
            result = get_avg_response_time(time_range)
            # 添加time_range信息到返回结果
            result['time_range'] = '当天' if time_range is None else time_range
            return Response(result)
        except Exception as e:
            return Response(
                {'error': f'获取平均响应时间失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def error_rate(self, request):
        """
        获取错误率
        """
        time_range_param = request.query_params.get('time_range')
        # 如果提供了time_range参数，则使用该参数，否则传入None，让utils函数使用默认的当天0点到当前时间
        time_range = int(time_range_param) if time_range_param is not None else None

        try:
            result = get_error_rate(time_range)
            # 添加time_range信息到返回结果
            result['time_range'] = '当天' if time_range is None else time_range
            return Response(result)
        except Exception as e:
            return Response(
                {'error': f'获取错误率失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def tps_and_response_time(self, request):
        """
        获取指定时间段内的TPS和平均响应时间
        支持的时间段：5m(默认), 1h, 1d, 1w
        """
        # 获取时间段参数
        time_period = request.query_params.get('time_period', '5m')

        # 验证时间段参数
        valid_periods = ['5m', '1h', '1d', '1w']
        if time_period not in valid_periods:
            return Response(
                {'error': f'无效的时间段参数: {time_period}. 有效值为: {valid_periods}'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # 获取TPS和响应时间数据
            result = get_tps_and_response_time(time_period)
            return Response(result)
        except Exception as e:
            return Response(
                {'error': f'获取TPS和响应时间数据失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def interface_tps_and_records(self, request):
        """
        获取按接口分组的TPS和响应时间数据，以及每个接口的最新5条调用记录
        支持的时间段：5m(默认), 1h, 1d, 1w
        支持分页查询，默认每页显示10个接口
        支持根据requestUrl过滤特定接口
        """
        # 获取时间段参数
        time_period = request.query_params.get('time_period', '5m')

        # 验证时间段参数
        valid_periods = ['5m', '1h', '1d', '1w']
        if time_period not in valid_periods:
            return Response(
                {'error': f'无效的时间段参数: {time_period}. 有效值为: {valid_periods}'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # 获取分页参数
        try:
            page = int(request.query_params.get('page', '1'))
            if page < 1:
                page = 1
        except ValueError:
            page = 1

        try:
            page_size = int(request.query_params.get('page_size', '10'))
            if page_size < 1:
                page_size = 10
            elif page_size > 100:  # 限制最大每页数量
                page_size = 100
        except ValueError:
            page_size = 10

        # 获取URL过滤参数
        filter_url = request.query_params.get('requestUrl', None)

        try:
            # 获取接口TPS和调用记录数据
            result = get_interface_tps_and_records(time_period, page, page_size, filter_url)
            return Response(result)
        except Exception as e:
            return Response(
                {'error': f'获取接口TPS和调用记录数据失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def operation_logs(self, request):
        """
        获取操作日志明细数据
        支持时间范围过滤，默认为当天数据
        支持分页查询，默认每页显示10条记录
        支持多种过滤条件
        """
        # 获取时间范围参数
        start_time = request.query_params.get('start_time', None)
        end_time = request.query_params.get('end_time', None)

        # 获取分页参数
        try:
            page = int(request.query_params.get('page', '1'))
            if page < 1:
                page = 1
        except ValueError:
            page = 1

        try:
            page_size = int(request.query_params.get('page_size', '10'))
            if page_size < 1:
                page_size = 10
            elif page_size > 100:  # 限制最大每页数量
                page_size = 100
        except ValueError:
            page_size = 10

        # 获取过滤条件
        filters = {}
        filter_fields = ['requestPath', 'accountId', 'ipAddress', 'result', 'operateType']
        for field in filter_fields:
            value = request.query_params.get(field, None)
            if value is not None:
                # 对于result字段，需要转换为整数
                if field == 'result' and value.isdigit():
                    filters[field] = int(value)
                else:
                    filters[field] = value

        try:
            # 获取操作日志数据
            result = get_operation_logs(start_time, end_time, page, page_size, filters)

            # 如果没有数据，添加提示信息
            if result['pagination']['total_count'] == 0:
                result['message'] = '没有找到符合条件的数据。请检查时间范围和过滤条件。'
                # 添加建议的时间范围
                from datetime import datetime, timedelta
                now = datetime.now()
                result['suggested_time_ranges'] = {
                    'today': {
                        'start_time': datetime(now.year, now.month, now.day, 0, 0, 0).strftime('%Y-%m-%d %H:%M:%S'),
                        'end_time': now.strftime('%Y-%m-%d %H:%M:%S')
                    },
                    'last_hour': {
                        'start_time': (now - timedelta(hours=1)).strftime('%Y-%m-%d %H:%M:%S'),
                        'end_time': now.strftime('%Y-%m-%d %H:%M:%S')
                    },
                    'last_24_hours': {
                        'start_time': (now - timedelta(hours=24)).strftime('%Y-%m-%d %H:%M:%S'),
                        'end_time': now.strftime('%Y-%m-%d %H:%M:%S')
                    }
                }

            return Response(result)
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"Error in operation_logs: {str(e)}\n{error_details}")
            return Response(
                {
                    'error': f'获取操作日志数据失败: {str(e)}',
                    'details': str(error_details),
                    'time_range': {
                        'start_time': start_time,
                        'end_time': end_time
                    }
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def user_behavior_analysis(self, request):
        """
        获取用户行为分析数据
        支持按用户ID、日期范围过滤
        返回用户一天内调用的所有请求，包括调用次数、平均操作时间等
        """
        # 获取过滤参数
        user_id = request.query_params.get('user_id', None)
        date_str = request.query_params.get('date', None)

        # 如果没有指定日期，则默认为当天
        if date_str:
            try:
                date = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                return Response(
                    {'error': f'无效的日期格式: {date_str}. 正确格式为: YYYY-MM-DD'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            date = datetime.now().date()

        # 构建Query
        query = Q(day=date)

        # 如果指定了用户ID，则添加用户过滤条件
        if user_id:
            try:
                user_id = int(user_id)
                query &= Q(request_user_id=user_id)
            except ValueError:
                return Response(
                    {'error': f'无效的用户ID: {user_id}. 应为整数'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # 获取分页参数
        try:
            page = int(request.query_params.get('page', '1'))
            if page < 1:
                page = 1
        except ValueError:
            page = 1

        try:
            page_size = int(request.query_params.get('page_size', '10'))
            if page_size < 1:
                page_size = 10
            elif page_size > 100:  # 限制最大每页数量
                page_size = 100
        except ValueError:
            page_size = 10

        try:
            # 查询数据
            queryset = UserOperateAnalysisDay.objects.filter(query).order_by('-request_count')

            # 计算总数
            total_count = queryset.count()

            # 分页
            start_idx = (page - 1) * page_size
            end_idx = start_idx + page_size
            page_data = queryset[start_idx:end_idx]

            # 序列化
            serializer = UserOperateAnalysisDaySerializer(page_data, many=True)

            # 计算总页数
            total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 0

            # 汇总统计信息
            total_requests = sum(item.request_count for item in page_data) if page_data else 0
            avg_time_overall = sum(item.avg_time * item.request_count for item in page_data) / total_requests if total_requests > 0 else 0

            result = {
                'data': serializer.data,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': total_pages
                },
                'summary': {
                    'date': date.strftime('%Y-%m-%d'),
                    'total_requests': total_requests,
                    'avg_time_overall': round(avg_time_overall, 2),
                    'user_id': user_id
                }
            }

            # 如果没有数据，添加提示信息
            if total_count == 0:
                result['message'] = '没有找到符合条件的数据。请检查日期和用户ID。'

            return Response(result)
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"Error in user_behavior_analysis: {str(e)}\n{error_details}")
            return Response(
                {
                    'error': f'获取用户行为分析数据失败: {str(e)}',
                    'details': str(error_details)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            
    @action(detail=False, methods=['get'])
    def feature_operation_analysis(self, request):
        """
        获取功能操作分析数据
        返回每个功能的操作数据，包括每天被调用次数、总操作人数和平均操作时间
        """
        # 获取日期参数
        date_str = request.query_params.get('date', None)
        
        try:
            # 获取功能操作分析数据
            result = get_feature_operation_analysis(date_str)
            
            # 如果没有数据，添加提示信息
            if len(result['data']) == 0:
                result['message'] = '没有找到符合条件的数据。请检查日期。'
                # 添加建议的日期范围
                from datetime import datetime, timedelta
                now = datetime.now()
                result['suggested_dates'] = {
                    'today': now.strftime('%Y-%m-%d'),
                    'yesterday': (now - timedelta(days=1)).strftime('%Y-%m-%d'),
                    'last_week': (now - timedelta(days=7)).strftime('%Y-%m-%d')
                }
            
            return Response(result)
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"Error in feature_operation_analysis: {str(e)}\n{error_details}")
            return Response(
                {
                    'error': f'获取功能操作分析数据失败: {str(e)}',
                    'details': str(error_details)
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
