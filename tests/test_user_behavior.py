# 用户行为分析测试模块

import unittest
from src.user_behavior import UserBehavior, analyze_group_behavior

class TestUserBehavior(unittest.TestCase):
    """测试用户行为分析类"""
    
    def setUp(self):
        """测试前准备"""
        self.user = UserBehavior(user_id="test_user_1")
    
    def test_add_behavior(self):
        """测试添加行为数据"""
        self.user.add_behavior({"type": "click", "page": "home", "timestamp": 1623456789})
        self.assertEqual(len(self.user.behaviors), 1)
    
    def test_analyze(self):
        """测试分析功能"""
        self.user.add_behavior({"type": "click", "page": "home", "timestamp": 1623456789})
        result = self.user.analyze()
        self.assertEqual(result["user_id"], "test_user_1")
        self.assertEqual(result["behavior_count"], 1)
    
    def test_group_analysis(self):
        """测试用户组分析"""
        user1 = UserBehavior(user_id="test_user_1")
        user1.add_behavior({"type": "click", "page": "home"})
        
        user2 = UserBehavior(user_id="test_user_2")
        user2.add_behavior({"type": "scroll", "page": "product"})
        
        result = analyze_group_behavior([user1, user2])
        self.assertEqual(result["user_count"], 2)
        self.assertEqual(len(result["individual_results"]), 2)


if __name__ == "__main__":
    unittest.main()