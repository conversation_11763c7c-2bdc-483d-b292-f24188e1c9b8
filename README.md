# User Behavior Analysis

这是一个用于分析用户行为的Python项目。

## 项目结构

```
userbehavior/
├── src/                # 源代码目录
│   └── __init__.py    # 使src成为一个包
├── tests/             # 测试代码目录
│   └── __init__.py    # 使tests成为一个包
├── docs/              # 文档目录
├── data/              # 数据目录
├── requirements.txt   # 项目依赖
├── setup.py           # 安装脚本
└── README.md          # 项目说明
```

## 安装

```bash
pip install -e .
```

## 使用方法

待补充