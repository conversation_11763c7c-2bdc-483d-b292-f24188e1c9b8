# 用户行为分析系统部署与运行指南

## 系统概述

用户行为分析系统由两个主要组件组成：
1. **Django Web应用** - 提供API接口和Web界面
2. **定时任务系统** - 负责定期执行数据分析任务

本文档将指导您如何部署和运行这两个组件。

## 系统要求

### 软件要求
- Python 3.8+
- MySQL 5.7+
- Elasticsearch 7.x

### Python依赖包
- Django 2.2.28
- PyMySQL 1.0.2
- elasticsearch-dsl
- pandas
- numpy
- 其他依赖包（详见requirements.txt）

## 部署步骤

### 1. 准备环境

#### 1.1 克隆代码仓库
```bash
git clone <repository_url>
cd userbehavior
```

#### 1.2 创建并激活虚拟环境（推荐）
```bash
# 创建虚拟环境
python -m venv venv

# 在Windows上激活虚拟环境
venv\Scripts\activate

# 在Linux/Mac上激活虚拟环境
source venv/bin/activate
```

#### 1.3 安装依赖包
```bash
pip install -r requirements.txt
```

### 2. 配置数据库

#### 2.1 创建MySQL数据库
```sql
CREATE DATABASE userbehavior CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'userbehavior'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON userbehavior.* TO 'userbehavior'@'localhost';
FLUSH PRIVILEGES;
```

#### 2.2 配置数据库连接
编辑 `src/config/settings.py` 文件，更新数据库配置：

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'userbehavior',
        'USER': 'userbehavior',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '3306',
        'OPTIONS': {
            'charset': 'utf8mb4',
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}
```

### 3. 配置Elasticsearch

#### 3.1 确保Elasticsearch服务正在运行

#### 3.2 配置Elasticsearch连接
编辑 `src/config/settings.py` 文件，更新Elasticsearch配置：

```python
ELASTICSEARCH_HOSTS = ['http://localhost:9200']
ELASTICSEARCH_USERNAME = 'elastic'  # 如果有设置
ELASTICSEARCH_PASSWORD = 'your_password'  # 如果有设置

# Elasticsearch索引配置
ELASTICSEARCH_INDICES = {
    'logs': 'tgtweb_apioperatelog'
}
```

### 4. 初始化数据库

运行数据库迁移命令，创建必要的表结构：

```bash
python manage.py migrate
```

### 5. 创建超级用户（可选）

如果需要访问Django管理界面，创建一个超级用户：

```bash
python manage.py createsuperuser
```

## 运行系统

系统包含两个主要组件，需要分别启动：

### 1. 启动Django Web应用

```bash
python run_server.py
nohup python3 run_server.py &
```

启动成功后，Django应用将在 http://localhost:8000 上运行。

### 2. 启动定时任务系统

```bash
python simple_scheduler.py
nohup   python simple_scheduler.py > scheduler.log 2>&1 &
```

定时任务系统将在后台运行，并在每天凌晨1:00自动执行数据分析任务。

### 3. 一键启动所有服务（可选）

如果您希望同时启动Django应用和定时任务系统，可以使用：

```bash
python start_all.py
```

这将同时启动两个组件，并监控它们的运行状态。

## 系统验证

### 1. 验证Django应用

访问以下URL，确认Django应用正常运行：

- 主页：http://localhost:8000/
- API文档：http://localhost:8000/api/

### 2. 验证定时任务系统

检查日志文件，确认定时任务系统正常运行：

```bash
# 查看定时任务系统日志
cat logs/scheduler/scheduler.log

# 查看任务执行日志
cat logs/task/task.log
```

## 系统监控

### 日志文件位置

- Django应用日志：
  - 一般日志：`logs/app.log`
  - 错误日志：`logs/error.log`
- 定时任务系统日志：`logs/scheduler/scheduler.log`
- 任务执行日志：`logs/task/task.log`

### 数据库监控

可以通过Django管理界面查看任务执行记录：

1. 访问 http://localhost:8000/admin/
2. 登录超级用户账号
3. 导航到 "Tasks" > "Task execution logs"

## API接口说明

系统提供以下主要API接口：

### 日志记录明细接口

- URL: `/api/analytics/log-details/`
- 方法: GET
- 参数:
  - `account_id`: 账号ID
  - `request_path`: 请求路径
  - `create_time_start`: 创建时间开始
  - `create_time_end`: 创建时间结束
  - `operate_type`: 操作类型
  - `result`: 结果
  - `min_cost`: 最小耗时（毫秒）
  - `system_type`: 系统类型，可选值为api(默认)、oss、boss
  - `page`: 页码，从1开始
  - `page_size`: 每页记录数

### 仪表盘接口

- 概览: `/api/analytics/dashboard/overview/`
- 活跃用户: `/api/analytics/dashboard/active-users/`
- 功能调用: `/api/analytics/dashboard/function-calls/`
- 平均响应时间: `/api/analytics/dashboard/avg-response-time/`
- 错误率: `/api/analytics/dashboard/error-rate/`
- TPS和响应时间: `/api/analytics/dashboard/tps-and-response-time/`
- 接口TPS和记录: `/api/analytics/dashboard/interface-tps-and-records/`
- 操作日志: `/api/analytics/dashboard/operation-logs/`
- 用户行为分析: `/api/analytics/dashboard/user-behavior-analysis/`
- 功能操作分析: `/api/analytics/dashboard/feature-operation-analysis/`
- 功能使用统计: `/api/analytics/dashboard/feature-usage-stats/`

## 常见问题

### 1. Django应用无法启动

**问题**: 运行`python run_server.py`时出现错误。

**解决方案**:
- 检查数据库连接配置是否正确
- 确保MySQL服务正在运行
- 检查日志文件中的详细错误信息

### 2. 定时任务系统无法连接到数据库

**问题**: 定时任务系统日志中显示数据库连接错误。

**解决方案**:
- 确保MySQL服务正在运行
- 检查数据库连接配置是否正确
- 确保数据库用户有足够的权限

### 3. Elasticsearch查询失败

**问题**: 系统日志中显示Elasticsearch查询错误。

**解决方案**:
- 确保Elasticsearch服务正在运行
- 检查Elasticsearch连接配置是否正确
- 确认索引名称是否正确

## 系统维护

### 数据库备份

建议定期备份MySQL数据库：

```bash
mysqldump -u userbehavior -p userbehavior > backup_$(date +%Y%m%d).sql
```

### 日志文件清理

系统日志文件会自动轮转，但建议定期检查日志目录，清理过旧的日志文件：

```bash
# 删除30天前的日志文件
find logs -name "*.log.*" -type f -mtime +30 -delete
```

## 联系与支持

如有任何问题或需要技术支持，请联系系统管理员或开发团队。

---

文档版本: 1.0
最后更新: 2025年4月28日
