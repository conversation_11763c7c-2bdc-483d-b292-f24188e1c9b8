# 用户行为分析数据生成任务需求与设计文档

## 1. 任务概述

### 1.1 任务名称
用户行为分析数据生成任务（北京时间01:00执行）

### 1.2 任务目的
每天自动从Elasticsearch中提取前一天的用户操作日志数据，进行聚合分析，生成用户行为分析数据，并将结果存储到数据库中，为系统提供用户行为分析的基础数据支持。

### 1.3 执行频率
每天北京时间01:00自动执行一次

### 1.4 任务类型
`user_behavior_analysis`

## 2. 数据流程

### 2.1 数据来源
- **数据源**: Elasticsearch
- **索引名**:
  - API系统: `tgtweb_apioperatelog-*`
  - OSS系统: `tgtweb_ossoperatelog-*`
  - BOSS系统: `tgtweb_operatelog-*`
- **时间范围**: 默认处理前一天（00:00:00至23:59:59）的数据

### 2.2 数据处理流程
1. 获取前一天的日期（或指定日期）
2. 从Elasticsearch中查询该日期范围内的操作日志数据
3. 使用Elasticsearch的聚合功能，按用户ID和请求路径分组
4. 计算每组的请求次数和平均响应时间
5. 提取每组的操作类型和类名等信息
6. 将聚合结果转换为标准格式
7. 删除数据库中该日期的旧数据（如果存在）
8. 将新的聚合数据批量插入数据库

### 2.3 数据目标
- **目标表**: `tgt_api_user_operate_analysis_day`（对应模型`UserOperateAnalysisDay`）
- **主要字段**:
  - `request_url`: 请求路径
  - `request_user_id`: 用户ID
  - `request_url_name`: 请求路径名称（使用方法名）
  - `request_type`: 请求类型（如GET、POST等）
  - `avg_time`: 平均响应时间（毫秒）
  - `request_count`: 请求次数
  - `day`: 统计日期
  - `created_time`: 记录创建时间
  - `system_type`: 系统类型（api、oss、boss）

## 3. 任务配置

### 3.1 Cron表达式
```
0 1 * * *
```
表示每天01:00执行（北京时间）

### 3.2 任务参数
```json
{
  "description": "每天北京时间01:00执行，生成前一天的用户行为分析数据"
}
```

### 3.3 可选参数
- `force_date`: 强制处理指定日期的数据，格式为`YYYY-MM-DD`
- `system_type`: 系统类型，可选值为`api`（默认）、`oss`、`boss`
- `all_systems`: 是否处理所有系统的数据，如果为`true`，则忽略`system_type`参数，默认为`false`

## 4. 实现细节

### 4.1 主要函数

#### 4.1.1 任务入口函数
```python
def user_behavior_analysis_task(params=None):
    """
    用户行为分析数据生成任务
    每天生成前一天的用户行为分析数据，并写入tgt_api_user_operate_analysis_day表

    参数:
        params (dict): 任务参数，可包含自定义的日期范围等
            force_date: 强制处理指定日期的数据，格式为YYYY-MM-DD
            system_type: 系统类型，可选值为api(默认)、oss、boss
            all_systems: 是否处理所有系统的数据，如果为True，则忽略system_type参数，默认为False

    返回:
        dict: 包含任务执行结果的字典
    """
```

#### 4.1.2 数据聚合函数
```python
def get_aggregated_data(date, system_type='api'):
    """
    使用Elasticsearch聚合功能获取聚合数据

    参数:
        date (date): 要处理的日期
        system_type (str): 系统类型，可选值为api(默认)、oss、boss

    返回:
        list: 聚合后的数据列表
    """
```

#### 4.1.3 数据插入函数
```python
def insert_user_behavior_data(date, aggregated_data, system_type='api'):
    """
    将用户行为分析数据插入数据库

    参数:
        date (date): 统计日期
        aggregated_data (list): 聚合后的数据列表
        system_type (str): 系统类型，可选值为api(默认)、oss、boss

    返回:
        int: 成功插入的记录数
    """
```

### 4.2 Elasticsearch查询

#### 4.2.1 基本查询
```python
index_name = ElasticsearchIndices.get_operation_log_wildcard(system_type)
field_mapping = ElasticsearchIndices.get_field_mapping(system_type)

s = Search(using=es_client, index=index_name) \
    .filter('range', **{field_mapping['create_time']: {'gte': start_time_str, 'lt': end_time_str}})
```

#### 4.2.2 聚合查询
```python
s.aggs.bucket('by_user', 'terms', field=field_mapping['user_id'], size=10000) \
      .bucket('by_path', 'terms', field=field_mapping['request_path'], size=10000) \
      .metric('avg_cost', 'avg', field=field_mapping['cost'])
```

### 4.3 数据库操作

#### 4.3.1 删除旧数据
```python
deleted = UserOperateAnalysisDay.objects.filter(day=date, system_type=system_type).delete()
```

#### 4.3.2 批量插入新数据
```python
with transaction.atomic():
    batch_size = 100
    for i in range(0, len(records), batch_size):
        batch = records[i:i+batch_size]
        UserOperateAnalysisDay.objects.bulk_create(batch)
```

## 5. 调度机制

### 5.1 任务调度器
系统使用`APScheduler`库的`BackgroundScheduler`作为任务调度器，通过`CronTrigger`触发器根据Cron表达式定时执行任务。

### 5.2 任务注册
任务在系统启动时通过`TaskScheduler.load_tasks()`方法从数据库中加载所有活跃的任务，并注册到调度器中。

### 5.3 任务执行流程
1. 调度器根据Cron表达式在指定时间触发任务
2. 创建任务执行日志记录，状态为"running"
3. 调用任务函数执行具体的业务逻辑
4. 更新任务执行日志记录，包括结束时间、状态和影响行数
5. 更新任务的最后执行时间和下次执行时间

### 5.4 错误处理
- 任务执行过程中的异常会被捕获并记录到日志中
- 任务执行日志记录的状态会被更新为"failed"，并记录错误信息
- 系统会继续保持调度器运行，不会因为单个任务失败而停止整个调度系统

## 6. 监控与日志

### 6.1 日志记录
任务执行过程中的关键步骤和异常情况会被记录到日志文件中，包括：
- 任务开始和结束时间
- 处理的数据日期
- 查询到的记录数量
- 聚合结果数量
- 插入数据库的记录数量
- 异常信息和堆栈跟踪

### 6.2 任务执行日志表
系统将任务的执行情况记录到`task_execution_log`表中，包括：
- 关联的任务ID
- 开始和结束时间
- 执行状态（running、success、failed）
- 影响的行数
- 错误信息（如果有）
- 执行详情

### 6.3 监控指标
可以通过以下指标监控任务的执行情况：
- 任务执行时长
- 处理的记录数量
- 成功率
- 错误类型和频率

## 7. 示例数据

### 7.1 输入数据示例（Elasticsearch日志记录）
```json
{
  "createTime": "2025-04-24 10:15:23",
  "cost": 145.67,
  "accountId": "admin",
  "requestPath": "/api/users/",
  "operateType": "GET",
  "className": "UserController",
  "methodName": "getUsers",
  "result": 1
}
```

### 7.2 聚合数据示例
```json
{
  "user_id": "admin",
  "request_path": "/api/users/",
  "doc_count": 156,
  "avg_cost": 145.67,
  "operate_type": "GET",
  "class_name": "UserController",
  "method_name": "getUsers"
}
```

### 7.3 输出数据示例（数据库记录）
```json
{
  "request_url": "/api/users/",
  "request_user_id": "admin",
  "request_url_name": "getUsers",
  "request_type": "GET",
  "avg_time": 146,
  "request_count": 156,
  "day": "2025-04-24",
  "created_time": "2025-04-25 01:00:43"
}
```

## 8. 性能考虑

### 8.1 Elasticsearch查询优化
- 使用精确的时间范围过滤，减少扫描的数据量
- 使用聚合功能在Elasticsearch端完成数据聚合，减少传输的数据量
- 设置合理的聚合桶大小（size=10000），避免数据截断

### 8.2 数据库操作优化
- 使用事务保证数据一致性
- 批量插入数据，减少数据库交互次数
- 默认批量大小为100，平衡内存使用和数据库负载

### 8.3 资源使用
- 任务在系统负载较低的凌晨01:00执行，减少对系统的影响
- 处理前一天的数据，数据量通常在可控范围内
- 使用分批处理大量数据，避免内存溢出

## 9. 扩展性与维护

### 9.1 参数配置
任务的关键参数（如执行时间、批处理大小）可以通过数据库中的任务配置进行调整，无需修改代码。

### 9.2 日期处理
支持处理指定日期的数据，便于补充生成历史数据或重新生成特定日期的数据。

### 9.3 错误恢复
任务执行失败后，可以通过手动触发或等待下次调度重新执行。

### 9.4 代码维护
任务的核心逻辑被拆分为多个功能明确的函数，便于维护和测试。

## 10. 相关接口

### 10.1 数据消费接口
- `/api/analytics/dashboard/feature-operation-analysis/` - 功能操作分析接口
- `/api/analytics/dashboard/user-behavior-analysis/` - 用户行为分析接口
- `/api/analytics/dashboard/feature-usage-stats/` - 功能使用统计接口

### 10.2 任务管理接口
- `/api/tasks/` - 获取任务列表
- `/api/tasks/{task_id}/` - 获取任务详情
- `/api/tasks/{task_id}/logs/` - 获取任务执行日志
- `/api/tasks/{task_id}/execute/` - 手动执行任务
