# 用户认证接口

## 接口概述

该接口提供用户认证相关功能，包括用户登录、获取用户信息、修改密码和登出等操作。系统采用JWT（JSON Web Token）认证机制，用户登录成功后获取访问令牌（access token）和刷新令牌（refresh token），后续请求通过携带访问令牌进行身份验证。

系统提供默认管理员账号，无需注册即可登录使用：
- 用户名：`admin`
- 密码：`tgt51848`

- **基础URL**: `/api/analytics/users/auth/`
- **认证方式**: JWT（Bearer Token）

## 功能需求

1. 提供用户登录功能，验证用户身份并颁发JWT令牌
2. 提供获取当前用户信息功能
3. 提供修改密码功能
4. 提供令牌刷新功能，延长用户会话有效期
5. 提供用户登出功能

## 接口列表

### 1. 用户登录

- **URL**: `/api/analytics/users/auth/login/`
- **方法**: POST
- **权限要求**: 无（公开接口）
- **功能描述**: 验证用户身份并颁发JWT令牌

#### 请求参数

**URL参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| system_type | String | 否 | 系统类型，可选值为api(默认)、oss、boss | "oss" |

**请求体参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| username | String | 是 | 用户名 | "admin" |
| password | String | 是 | 密码 | "tgt51848" |

#### 响应结果

##### 成功响应

**状态码**: 200 OK

**响应体**:

```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "real_name": "张三",
    "phone": "13800138000",
    "date_joined": "2023-05-01T10:00:00",
    "last_login": "2023-05-02T15:30:00"
  }
}
```

##### 失败响应

**状态码**: 400 Bad Request

**响应体**:

```json
{
  "non_field_errors": [
    "无法使用提供的凭据登录"
  ]
}
```

### 3. 获取当前用户信息

- **URL**: `/api/analytics/users/auth/me/`
- **方法**: GET
- **权限要求**: 已认证用户
- **功能描述**: 获取当前登录用户的详细信息

#### 请求头

| 参数名 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- |
| Authorization | 是 | Bearer认证头，格式为"Bearer {access_token}" | "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..." |

#### 响应结果

##### 成功响应

**状态码**: 200 OK

**响应体**:

```json
{
  "id": 1,
  "username": "admin",
  "email": "<EMAIL>",
  "real_name": "张三",
  "phone": "13800138000",
  "date_joined": "2023-05-01T10:00:00",
  "last_login": "2023-05-02T15:30:00"
}
```

##### 失败响应

**状态码**: 401 Unauthorized

**响应体**:

```json
{
  "detail": "身份认证信息未提供。"
}
```

### 4. 修改密码

- **URL**: `/api/analytics/users/auth/change_password/`
- **方法**: POST
- **权限要求**: 已认证用户
- **功能描述**: 修改当前用户的密码

#### 请求头

| 参数名 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- |
| Authorization | 是 | Bearer认证头，格式为"Bearer {access_token}" | "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..." |

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| old_password | String | 是 | 旧密码 | "password123" |
| new_password | String | 是 | 新密码 | "newpassword456" |
| new_password_confirm | String | 是 | 确认新密码，必须与new_password一致 | "newpassword456" |

#### 响应结果

##### 成功响应

**状态码**: 200 OK

**响应体**:

```json
{
  "detail": "密码修改成功"
}
```

##### 失败响应

**状态码**: 400 Bad Request

**响应体**:

```json
{
  "old_password": [
    "旧密码不正确"
  ],
  "new_password_confirm": [
    "两次密码不一致"
  ]
}
```

### 5. 刷新令牌

- **URL**: `/api/analytics/users/auth/token/refresh/`
- **方法**: POST
- **权限要求**: 无（公开接口，但需要有效的刷新令牌）
- **功能描述**: 使用刷新令牌获取新的访问令牌

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| refresh | String | 是 | 刷新令牌 | "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..." |

#### 响应结果

##### 成功响应

**状态码**: 200 OK

**响应体**:

```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

##### 失败响应

**状态码**: 401 Unauthorized

**响应体**:

```json
{
  "detail": "令牌无效或已过期",
  "code": "token_not_valid"
}
```

### 6. 用户登出

- **URL**: `/api/analytics/users/auth/logout/`
- **方法**: POST
- **权限要求**: 已认证用户
- **功能描述**: 用户登出系统

#### 请求头

| 参数名 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- |
| Authorization | 是 | Bearer认证头，格式为"Bearer {access_token}" | "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..." |

#### 响应结果

##### 成功响应

**状态码**: 200 OK

**响应体**:

```json
{
  "detail": "登出成功"
}
```

## 使用示例

### 示例1: 用户登录

**请求**:
```
POST /api/analytics/users/auth/login/?system_type=oss
Content-Type: application/json

{
  "username": "admin",
  "password": "tgt51848"
}
```

**响应**:
```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "real_name": "张三",
    "phone": "13800138000",
    "date_joined": "2023-05-01T10:00:00",
    "last_login": "2023-05-03T16:45:00"
  },
  "system_type": "oss"
}
```

### 示例2: 获取当前用户信息

**请求**:
```
GET /api/analytics/users/auth/me/
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**响应**:
```json
{
  "id": 1,
  "username": "admin",
  "email": "<EMAIL>",
  "real_name": "张三",
  "phone": "13800138000",
  "date_joined": "2023-05-01T10:00:00",
  "last_login": "2023-05-03T16:45:00"
}
```

### 示例3: 修改密码

**请求**:
```
POST /api/analytics/users/auth/change_password/
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
Content-Type: application/json

{
  "old_password": "password123",
  "new_password": "newpassword456",
  "new_password_confirm": "newpassword456"
}
```

**响应**:
```json
{
  "detail": "密码修改成功"
}
```

### 示例4: 刷新令牌

**请求**:
```
POST /api/analytics/users/auth/token/refresh/
Content-Type: application/json

{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**响应**:
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### 示例5: 用户登出

**请求**:
```
POST /api/analytics/users/auth/logout/
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**响应**:
```json
{
  "detail": "登出成功"
}
```

## 注意事项

1. JWT令牌有效期设置：
   - 访问令牌（access token）有效期为1小时
   - 刷新令牌（refresh token）有效期为7天

2. 认证头格式：
   - 必须使用Bearer认证方式
   - 格式为：`Authorization: Bearer {access_token}`

3. 密码安全要求：
   - 密码长度至少为8个字符
   - 建议使用字母、数字和特殊字符的组合

4. 令牌刷新：
   - 当访问令牌过期时，应使用刷新令牌获取新的访问令牌
   - 如果刷新令牌也过期，用户需要重新登录

5. 登出处理：
   - 在JWT认证机制中，服务器端不保存令牌状态
   - 客户端应在登出时删除本地存储的令牌

6. 错误处理：
   - 所有接口在遇到错误时会返回适当的HTTP状态码和详细的错误信息
   - 客户端应妥善处理这些错误信息并向用户展示友好的提示
