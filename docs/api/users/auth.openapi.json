{"openapi": "3.1.0", "info": {"title": "用户认证接口", "description": "该接口提供用户认证相关功能，包括用户登录、获取用户信息、修改密码和登出等操作。系统采用JWT（JSON Web Token）认证机制，用户登录成功后获取访问令牌（access token）和刷新令牌（refresh token），后续请求通过携带访问令牌进行身份验证。\n\n系统提供默认管理员账号，无需注册即可登录使用：\n- 用户名：`admin`\n- 密码：`tgt51848`", "version": "1.0.0"}, "tags": [{"name": "auth", "description": "用户认证相关接口"}], "paths": {"/api/analytics/users/auth/login/": {"post": {"summary": "用户登录", "description": "验证用户身份并颁发JWT令牌", "parameters": [{"name": "system_type", "in": "query", "description": "系统类型，可选值为api(默认)、oss、boss", "required": false, "schema": {"type": "string", "enum": ["api", "oss", "boss"], "default": "api"}}], "tags": ["auth"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLogin"}}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "400": {"description": "登录失败", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationError"}}}}}}}, "/api/analytics/users/auth/me/": {"get": {"summary": "获取当前用户信息", "description": "获取当前登录用户的详细信息", "tags": ["auth"], "security": [{"BearerAuth": []}], "responses": {"200": {"description": "成功获取用户信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthError"}}}}}}}, "/api/analytics/users/auth/change_password/": {"post": {"summary": "修改密码", "description": "修改当前用户的密码", "tags": ["auth"], "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordChange"}}}}, "responses": {"200": {"description": "密码修改成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"detail": {"type": "string", "example": "密码修改成功"}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidationError"}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthError"}}}}}}}, "/api/analytics/users/auth/token/refresh/": {"post": {"summary": "刷新令牌", "description": "使用刷新令牌获取新的访问令牌", "tags": ["auth"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenRefresh"}}}}, "responses": {"200": {"description": "令牌刷新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"access": {"type": "string", "example": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."}}}}}}, "401": {"description": "令牌无效或已过期", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenError"}}}}}}}, "/api/analytics/users/auth/logout/": {"post": {"summary": "用户登出", "description": "用户登出系统", "tags": ["auth"], "security": [{"BearerAuth": []}], "responses": {"200": {"description": "登出成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"detail": {"type": "string", "example": "登出成功"}}}}}}, "401": {"description": "未认证", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthError"}}}}}}}}, "components": {"schemas": {"User": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "example": 1}, "username": {"type": "string", "example": "admin"}, "email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "real_name": {"type": "string", "example": "张三"}, "phone": {"type": "string", "example": "13800138000"}, "date_joined": {"type": "string", "format": "date-time", "example": "2023-05-01T10:00:00"}, "last_login": {"type": "string", "format": "date-time", "nullable": true, "example": "2023-05-02T15:30:00"}}}, "UserLogin": {"type": "object", "required": ["username", "password"], "properties": {"username": {"type": "string", "example": "admin"}, "password": {"type": "string", "format": "password", "example": "tgt51848"}}}, "PasswordChange": {"type": "object", "required": ["old_password", "new_password", "new_password_confirm"], "properties": {"old_password": {"type": "string", "format": "password", "example": "password123"}, "new_password": {"type": "string", "format": "password", "example": "newpassword456"}, "new_password_confirm": {"type": "string", "format": "password", "example": "newpassword456"}}}, "TokenResponse": {"type": "object", "properties": {"refresh": {"type": "string", "example": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."}, "access": {"type": "string", "example": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."}, "user": {"$ref": "#/components/schemas/User"}, "system_type": {"type": "string", "enum": ["api", "oss", "boss"], "example": "api"}}}, "TokenRefresh": {"type": "object", "required": ["refresh"], "properties": {"refresh": {"type": "string", "example": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."}}}, "ValidationError": {"type": "object", "properties": {"username": {"type": "array", "items": {"type": "string"}, "example": ["该用户名已存在"]}, "password_confirm": {"type": "array", "items": {"type": "string"}, "example": ["两次密码不一致"]}, "non_field_errors": {"type": "array", "items": {"type": "string"}, "example": ["无法使用提供的凭据登录"]}}}, "AuthError": {"type": "object", "properties": {"detail": {"type": "string", "example": "身份认证信息未提供。"}}}, "TokenError": {"type": "object", "properties": {"detail": {"type": "string", "example": "令牌无效或已过期"}, "code": {"type": "string", "example": "token_not_valid"}}}}, "securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}