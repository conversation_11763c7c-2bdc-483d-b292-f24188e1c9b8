{"openapi": "3.1.0", "info": {"title": "功能操作分析接口", "description": "该接口提供系统中各功能的操作分析数据，包括每个功能的调用次数、操作人数和平均操作时间等指标。这些数据可以帮助用户了解系统中哪些功能使用频率最高、哪些功能响应时间最长，从而进行有针对性的优化和改进。", "version": "1.0.0"}, "tags": [{"name": "analytics", "description": "分析统计相关接口"}], "paths": {"/api/analytics/dashboard/feature-operation-analysis/": {"get": {"summary": "获取功能操作分析数据", "description": "提供特定日期的功能操作分析数据，包括每个功能的调用次数、操作人数和平均操作时间等指标。", "tags": ["analytics"], "parameters": [{"name": "date", "in": "query", "description": "查询日期，格式：YYYY-MM-DD，默认为当天", "required": false, "example": "2023-04-23", "schema": {"type": "string", "format": "date"}}, {"name": "request_url", "in": "query", "description": "请求URL过滤条件，支持模糊查询", "required": false, "example": "/api/users", "schema": {"type": "string"}}, {"name": "request_type", "in": "query", "description": "请求类型过滤条件，如GET、POST等", "required": false, "example": "GET", "schema": {"type": "string"}}, {"name": "system_type", "in": "query", "description": "系统类型，可选值为api(默认)、oss、boss", "required": false, "example": "boss", "schema": {"type": "string", "enum": ["api", "oss", "boss"], "default": "api"}}, {"name": "page", "in": "query", "description": "当前页码，从1开始，默认为1", "required": false, "example": 2, "schema": {"type": "integer", "default": 1, "minimum": 1}}, {"name": "page_size", "in": "query", "description": "每页显示的记录数量，默认为10，最大为100", "required": false, "example": 20, "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 100}}], "responses": {"200": {"description": "成功获取功能操作分析数据", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FeatureOperationAnalysisResponse"}, "examples": {"success": {"summary": "成功响应示例", "value": {"data": [{"request_url": "/api/users/", "request_type": "GET", "request_url_name": "用户列表", "avg_time": 156.78, "total_requests": 1245, "user_count": 78, "date": "2023-04-23"}, {"request_url": "/api/features/", "request_type": "GET", "request_url_name": "功能列表", "avg_time": 123.45, "total_requests": 987, "user_count": 65, "date": "2023-04-23"}], "pagination": {"page": 1, "page_size": 10, "total_count": 45, "total_pages": 5}, "summary": {"date": "2023-04-23", "total_features": 45, "total_requests": 12450, "avg_time_overall": 187.45, "system_type": "api"}}}, "empty": {"summary": "无数据响应示例", "value": {"data": [], "summary": {"date": "2023-04-23", "total_features": 0, "total_requests": 0, "avg_time_overall": 0, "system_type": "api"}, "message": "没有找到符合条件的数据。请检查日期。", "suggested_dates": {"today": "2023-04-24", "yesterday": "2023-04-23", "last_week": "2023-04-17"}}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "无效的日期格式: 2023-04-32. 正确格式为: YYYY-MM-DD"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "获取功能操作分析数据失败: [错误详情]", "details": "[详细错误信息]"}}}}}}}}, "components": {"schemas": {"FeatureOperationAnalysisResponse": {"type": "object", "required": ["data", "pagination", "summary"], "properties": {"data": {"type": "array", "description": "功能操作分析数据数组", "items": {"$ref": "#/components/schemas/FeatureOperationAnalysisItem"}}, "pagination": {"$ref": "#/components/schemas/Pagination", "description": "分页信息"}, "summary": {"$ref": "#/components/schemas/Summary", "description": "汇总统计信息"}, "message": {"type": "string", "description": "提示信息（当没有数据时）"}, "suggested_dates": {"type": "object", "description": "建议的日期范围（当没有数据时）", "properties": {"today": {"type": "string", "description": "今天的日期", "example": "2023-04-24"}, "yesterday": {"type": "string", "description": "昨天的日期", "example": "2023-04-23"}, "last_week": {"type": "string", "description": "一周前的日期", "example": "2023-04-17"}}}}}, "FeatureOperationAnalysisItem": {"type": "object", "required": ["request_url", "request_type", "avg_time", "total_requests", "user_count", "date"], "properties": {"request_url": {"type": "string", "description": "请求URL", "example": "/api/users/"}, "request_type": {"type": "string", "description": "请求类型，如GET、POST等", "example": "GET"}, "request_url_name": {"type": "string", "description": "请求URL的名称，如果没有则显示URL", "example": "用户列表"}, "avg_time": {"type": "number", "format": "float", "description": "平均操作时间（毫秒）", "example": 156.78}, "total_requests": {"type": "integer", "description": "总请求次数", "example": 1245}, "user_count": {"type": "integer", "description": "操作人数（不同用户ID的数量）", "example": 78}, "date": {"type": "string", "format": "date", "description": "统计日期，格式：YYYY-MM-DD", "example": "2023-04-23"}}}, "Pagination": {"type": "object", "required": ["page", "page_size", "total_count", "total_pages"], "properties": {"page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页记录数", "example": 10}, "total_count": {"type": "integer", "description": "总记录数", "example": 45}, "total_pages": {"type": "integer", "description": "总页数", "example": 5}}}, "Summary": {"type": "object", "required": ["date", "total_features", "total_requests", "avg_time_overall", "system_type"], "properties": {"date": {"type": "string", "format": "date", "description": "统计日期，格式：YYYY-MM-DD", "example": "2023-04-23"}, "total_features": {"type": "integer", "description": "总功能数", "example": 45}, "total_requests": {"type": "integer", "description": "总请求次数", "example": 12450}, "avg_time_overall": {"type": "number", "format": "float", "description": "整体平均操作时间（毫秒）", "example": 187.45}, "system_type": {"type": "string", "description": "系统类型", "example": "api", "enum": ["api", "oss", "boss"]}}}, "ErrorResponse": {"type": "object", "required": ["error"], "properties": {"error": {"type": "string", "description": "错误信息", "example": "无效的日期格式: 2023-04-32. 正确格式为: YYYY-MM-DD"}, "details": {"type": "string", "description": "详细错误信息", "example": "[详细错误信息]"}}}}}, "servers": [{"url": "http://localhost:8000", "description": "本地开发服务器"}]}