# 耗时功能排行接口

## 接口概述

该接口提供系统中耗时最长的功能排行榜，按平均响应时间降序排序。通过这个接口，用户可以快速识别系统中响应时间较长的功能，从而进行针对性的性能优化。接口支持设置最小平均耗时和最小请求次数阈值，以过滤出真正需要关注的性能问题。

- **URL**: `/api/analytics/slow-functions/`
- **方法**: GET
- **权限要求**: 无特殊权限要求

## 功能需求

1. 提供指定时间范围内耗时最长的功能排行榜
2. 支持设置最小平均耗时阈值，只返回平均耗时大于该阈值的功能
3. 支持设置最小请求次数阈值，只返回请求次数大于该阈值的功能
4. 按平均耗时降序排序，便于快速识别最需要优化的功能
5. 提供每个功能的平均耗时、最大耗时、最小耗时和请求次数等详细信息
6. 支持分页查询，默认每页显示10条记录
7. 当没有数据时，提供友好的提示信息

## 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| start_time | String | 是 | 开始时间，格式：YYYY-MM-DD HH:MM:SS | 2023-04-24 00:00:00 |
| end_time | String | 是 | 结束时间，格式：YYYY-MM-DD HH:MM:SS | 2023-04-24 23:59:59 |
| min_avg_cost | Float | 否 | 最小平均耗时阈值（毫秒），默认为100 | 500 |
| min_count | Integer | 否 | 最小请求次数阈值，默认为2 | 5 |
| system_type | String | 否 | 系统类型，可选值为api(默认)、oss、boss | boss |
| page | Integer | 否 | 当前页码，从1开始，默认为1 | 2 |
| page_size | Integer | 否 | 每页显示的记录数量，默认为10，最大为100 | 20 |

## 响应结果

### 成功响应

**状态码**: 200 OK

**响应体**:

```json
{
    "data": [
        {
            "requestPath": "/api/reports/generate/",
            "avgCost": 2356.78,
            "maxCost": 5432.10,
            "minCost": 987.65,
            "count": 42,
            "totalCost": 98984.76
        },
        {
            "requestPath": "/api/analytics/log-details/",
            "avgCost": 1234.56,
            "maxCost": 3456.78,
            "minCost": 765.43,
            "count": 87,
            "totalCost": 107406.72
        },
        // ... 更多功能记录
    ],
    "pagination": {
        "page": 1,
        "page_size": 10,
        "total_count": 15,
        "total_pages": 2
    },
    "summary": {
        "start_time": "2023-04-24 00:00:00",
        "end_time": "2023-04-24 23:59:59",
        "min_avg_cost": 100,
        "min_count": 2,
        "total_functions": 15
    }
}
```

**当没有数据时的响应**:

```json
{
    "data": [],
    "pagination": {
        "page": 1,
        "page_size": 10,
        "total_count": 0,
        "total_pages": 0
    },
    "summary": {
        "start_time": "2023-04-24 00:00:00",
        "end_time": "2023-04-24 23:59:59",
        "min_avg_cost": 100,
        "min_count": 2,
        "total_functions": 0
    },
    "message": "没有找到符合条件的数据。请检查查询参数或调整时间范围。"
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
| ----- | ---- | ---- |
| data | Array | 耗时功能排行数据数组 |
| data[].requestPath | String | 请求路径 |
| data[].avgCost | Float | 平均耗时（毫秒），保留两位小数 |
| data[].maxCost | Float | 最大耗时（毫秒），保留两位小数 |
| data[].minCost | Float | 最小耗时（毫秒），保留两位小数 |
| data[].count | Integer | 请求次数 |
| data[].totalCost | Float | 总耗时（毫秒），保留两位小数 |
| pagination | Object | 分页信息 |
| pagination.page | Integer | 当前页码 |
| pagination.page_size | Integer | 每页记录数 |
| pagination.total_count | Integer | 总记录数 |
| pagination.total_pages | Integer | 总页数 |
| summary | Object | 查询摘要 |
| summary.start_time | String | 开始时间，格式：YYYY-MM-DD HH:MM:SS |
| summary.end_time | String | 结束时间，格式：YYYY-MM-DD HH:MM:SS |
| summary.min_avg_cost | Float | 最小平均耗时阈值（毫秒） |
| summary.min_count | Integer | 最小请求次数阈值 |
| summary.total_functions | Integer | 符合条件的功能总数 |
| message | String | 提示信息（当没有数据时） |

### 失败响应

**状态码**: 400 Bad Request

**响应体**:

```json
{
    "error": "缺少必要的参数: start_time"
}
```

或

```json
{
    "error": "无效的时间格式: 2023-04-24. 正确格式为: YYYY-MM-DD HH:MM:SS"
}
```

或

```json
{
    "error": "时间范围最多为1天"
}
```

**状态码**: 500 Internal Server Error

**响应体**:

```json
{
    "error": "查询失败: [错误详情]"
}
```

## 实现细节

### 数据来源

该接口从 Elasticsearch 中获取数据，使用的索引为系统操作日志索引（`tgtweb_apioperatelog`）。

### 时间处理

1. **时间格式**：
   - 支持 `YYYY-MM-DD HH:MM:SS` 格式
   - 会验证时间的有效性
   - 确保开始时间早于结束时间

2. **时间范围限制**：
   - 时间范围最长为1天
   - 这是为了保证查询性能和避免返回过多数据

### 聚合计算逻辑

1. **按请求路径分组**：
   - 使用 Elasticsearch 的 `terms` 聚合按 `requestPath` 字段分组
   - 获取每个路径的请求次数

2. **计算耗时指标**：
   - 平均耗时：使用 `avg` 聚合计算每个路径的平均 `cost` 值
   - 最大耗时：使用 `max` 聚合计算每个路径的最大 `cost` 值
   - 最小耗时：使用 `min` 聚合计算每个路径的最小 `cost` 值
   - 总耗时：使用 `sum` 聚合计算每个路径的总 `cost` 值

3. **过滤逻辑**：
   - 只保留平均耗时大于等于 `min_avg_cost` 的路径
   - 只保留请求次数大于等于 `min_count` 的路径
   - 这样可以过滤掉不需要关注的路径

### 排序和分页

1. **排序**：
   - 按平均耗时降序排序，最耗时的功能排在前面
   - 这样可以快速识别最需要优化的功能

2. **分页**：
   - 支持标准的分页参数 `page` 和 `page_size`
   - 默认每页显示10条记录，最大为100条
   - 分页在内存中进行，先获取所有符合条件的数据，再进行分页

## 使用示例

### 示例1: 获取平均耗时大于500毫秒的功能

**请求**:
```
GET /api/analytics/slow-functions/?start_time=2023-04-24%2000:00:00&end_time=2023-04-24%2023:59:59&min_avg_cost=500
```

**响应**:
```json
{
    "data": [
        {
            "requestPath": "/api/reports/generate/",
            "avgCost": 2356.78,
            "maxCost": 5432.10,
            "minCost": 987.65,
            "count": 42,
            "totalCost": 98984.76
        },
        {
            "requestPath": "/api/analytics/log-details/",
            "avgCost": 1234.56,
            "maxCost": 3456.78,
            "minCost": 765.43,
            "count": 87,
            "totalCost": 107406.72
        },
        // ... 更多功能记录
    ],
    "pagination": {
        "page": 1,
        "page_size": 10,
        "total_count": 15,
        "total_pages": 2
    },
    "summary": {
        "start_time": "2023-04-24 00:00:00",
        "end_time": "2023-04-24 23:59:59",
        "min_avg_cost": 500,
        "min_count": 2,
        "total_functions": 15
    }
}
```

### 示例2: 获取请求次数大于10次且平均耗时大于200毫秒的功能

**请求**:
```
GET /api/analytics/slow-functions/?start_time=2023-04-24%2000:00:00&end_time=2023-04-24%2023:59:59&min_avg_cost=200&min_count=10
```

**响应**:
```json
{
    "data": [
        {
            "requestPath": "/api/analytics/log-details/",
            "avgCost": 1234.56,
            "maxCost": 3456.78,
            "minCost": 765.43,
            "count": 87,
            "totalCost": 107406.72
        },
        {
            "requestPath": "/api/features/usage/",
            "avgCost": 876.54,
            "maxCost": 1987.65,
            "minCost": 543.21,
            "count": 65,
            "totalCost": 56975.10
        },
        // ... 更多功能记录
    ],
    "pagination": {
        "page": 1,
        "page_size": 10,
        "total_count": 8,
        "total_pages": 1
    },
    "summary": {
        "start_time": "2023-04-24 00:00:00",
        "end_time": "2023-04-24 23:59:59",
        "min_avg_cost": 200,
        "min_count": 10,
        "total_functions": 8
    }
}
```

### 示例3: 分页查询

**请求**:
```
GET /api/analytics/slow-functions/?start_time=2023-04-24%2000:00:00&end_time=2023-04-24%2023:59:59&page=2&page_size=5
```

**响应**:
```json
{
    "data": [
        // ... 第2页的5条记录
    ],
    "pagination": {
        "page": 2,
        "page_size": 5,
        "total_count": 15,
        "total_pages": 3
    },
    "summary": {
        "start_time": "2023-04-24 00:00:00",
        "end_time": "2023-04-24 23:59:59",
        "min_avg_cost": 100,
        "min_count": 2,
        "total_functions": 15
    }
}
```

## 相关接口

- `/api/analytics/dashboard/avg-response-time/` - 获取系统平均响应时间数据
- `/api/analytics/dashboard/interface-tps-and-records/` - 获取按接口分组的TPS和响应时间数据
- `/api/features/performance/` - 获取功能性能指标
- `/api/analytics/dashboard/feature-operation-analysis/` - 获取功能操作分析数据

## 注意事项

1. 时间范围最长为1天，这是为了保证查询性能和避免返回过多数据
2. 开始时间和结束时间是必填参数，且必须符合 `YYYY-MM-DD HH:MM:SS` 格式
3. 默认的最小平均耗时阈值为100毫秒，最小请求次数阈值为2次
4. 数据按平均耗时降序排序，最耗时的功能排在前面
5. 所有耗时数据均保留两位小数
6. 当没有数据时，系统会提供友好的提示信息
7. 该接口主要用于性能优化和问题排查，建议定期查看以发现潜在的性能瓶颈
