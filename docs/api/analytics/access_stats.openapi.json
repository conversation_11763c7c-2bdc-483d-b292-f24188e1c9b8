{"openapi": "3.1.0", "info": {"title": "访问统计分析接口", "description": "该接口提供特定用户在指定时间范围内访问某个功能的时间序列统计数据，按分钟粒度进行聚合。通过这个接口，可以分析用户的访问模式、高峰期使用情况，以及特定功能的使用频率变化趋势，为用户行为分析和系统负载优化提供数据支持。", "version": "1.0.0"}, "tags": [{"name": "analytics", "description": "分析统计相关接口"}], "paths": {"/api/analytics/access-stats/": {"get": {"summary": "获取访问统计分析数据", "description": "提供特定用户在指定时间范围内访问某个功能的分钟级统计数据，按分钟粒度进行聚合。", "tags": ["analytics"], "parameters": [{"name": "start_time", "in": "query", "description": "开始时间，格式：YYYY-MM-DD HH:MM:SS", "required": true, "example": "2023-04-24 00:00:00", "schema": {"type": "string", "format": "date-time"}}, {"name": "end_time", "in": "query", "description": "结束时间，格式：YYYY-MM-DD HH:MM:SS", "required": true, "example": "2023-04-24 01:00:00", "schema": {"type": "string", "format": "date-time"}}, {"name": "account_id", "in": "query", "description": "账号ID", "required": true, "example": "admin", "schema": {"type": "string"}}, {"name": "request_path", "in": "query", "description": "请求路径", "required": true, "example": "/api/users/", "schema": {"type": "string"}}, {"name": "min_count", "in": "query", "description": "最小访问次数，只返回访问次数大于等于此值的记录，默认为1", "required": false, "example": 5, "schema": {"type": "integer", "default": 1, "minimum": 1}}, {"name": "system_type", "in": "query", "description": "系统类型，可选值为api(默认)、oss、boss", "required": false, "example": "boss", "schema": {"type": "string", "enum": ["api", "oss", "boss"], "default": "api"}}, {"name": "page", "in": "query", "description": "当前页码，从1开始，默认为1", "required": false, "example": 2, "schema": {"type": "integer", "default": 1, "minimum": 1}}, {"name": "page_size", "in": "query", "description": "每页显示的记录数量，默认为10，最大为100", "required": false, "example": 20, "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 100}}], "responses": {"200": {"description": "成功获取访问统计数据", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccessStatsResponse"}, "examples": {"success": {"summary": "成功响应示例", "value": {"data": [{"timestamp": "2023-04-24 00:01:00", "count": 5}, {"timestamp": "2023-04-24 00:02:00", "count": 8}, {"timestamp": "2023-04-24 00:03:00", "count": 12}], "pagination": {"page": 1, "page_size": 10, "total_count": 35, "total_pages": 4}, "summary": {"account_id": "admin", "request_path": "/api/users/", "start_time": "2023-04-24 00:00:00", "end_time": "2023-04-24 01:00:00", "min_count": 1, "total_minutes": 35}}}, "empty": {"summary": "无数据响应示例", "value": {"data": [], "pagination": {"page": 1, "page_size": 10, "total_count": 0, "total_pages": 0}, "summary": {"account_id": "admin", "request_path": "/api/users/", "start_time": "2023-04-24 00:00:00", "end_time": "2023-04-24 01:00:00", "min_count": 1, "total_minutes": 0}, "message": "没有找到符合条件的数据。请检查查询参数或调整时间范围。"}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"missing_params": {"summary": "缺少必填参数", "value": {"error": "缺少必填参数: start_time, end_time, account_id, request_path 都是必填的"}}, "invalid_time_format": {"summary": "时间格式错误", "value": {"error": "时间格式不正确，正确格式为: YYYY-MM-DD HH:MM:SS"}}, "time_range_too_small": {"summary": "时间范围过小", "value": {"error": "时间范围至少需要1分钟"}}, "time_range_too_large": {"summary": "时间范围过大", "value": {"error": "时间范围最多为24小时"}}}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "查询失败: 内部服务器错误"}}}}}}}}, "components": {"schemas": {"AccessStatsResponse": {"type": "object", "required": ["data", "pagination", "summary"], "properties": {"data": {"type": "array", "description": "访问统计数据数组", "items": {"$ref": "#/components/schemas/AccessStatsItem"}}, "pagination": {"$ref": "#/components/schemas/Pagination", "description": "分页信息"}, "summary": {"$ref": "#/components/schemas/Summary", "description": "查询摘要"}, "message": {"type": "string", "description": "提示信息（当没有数据时）"}}}, "AccessStatsItem": {"type": "object", "required": ["timestamp", "count"], "properties": {"timestamp": {"type": "string", "description": "时间戳，格式：YYYY-MM-DD HH:MM:SS", "example": "2023-04-24 00:01:00"}, "count": {"type": "integer", "description": "该分钟内的访问次数", "example": 5}}}, "Pagination": {"type": "object", "required": ["page", "page_size", "total_count", "total_pages"], "properties": {"page": {"type": "integer", "description": "当前页码", "example": 1}, "page_size": {"type": "integer", "description": "每页记录数", "example": 10}, "total_count": {"type": "integer", "description": "总记录数", "example": 35}, "total_pages": {"type": "integer", "description": "总页数", "example": 4}}}, "Summary": {"type": "object", "required": ["account_id", "request_path", "start_time", "end_time", "min_count", "total_minutes"], "properties": {"account_id": {"type": "string", "description": "查询的账号ID", "example": "admin"}, "request_path": {"type": "string", "description": "查询的请求路径", "example": "/api/users/"}, "start_time": {"type": "string", "description": "开始时间，格式：YYYY-MM-DD HH:MM:SS", "example": "2023-04-24 00:00:00"}, "end_time": {"type": "string", "description": "结束时间，格式：YYYY-MM-DD HH:MM:SS", "example": "2023-04-24 01:00:00"}, "min_count": {"type": "integer", "description": "最小访问次数", "example": 1}, "total_minutes": {"type": "integer", "description": "有访问记录的分钟数", "example": 35}}}, "ErrorResponse": {"type": "object", "required": ["error"], "properties": {"error": {"type": "string", "description": "错误信息", "example": "缺少必填参数: start_time, end_time, account_id, request_path 都是必填的"}}}}}, "servers": [{"url": "http://localhost:8000", "description": "本地开发服务器"}]}