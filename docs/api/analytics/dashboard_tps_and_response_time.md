# TPS和响应时间接口

## 接口概述

该接口提供系统在指定时间段内的每秒事务处理量(TPS)和平均响应时间的时间序列数据。这些数据可以帮助用户监控系统性能，识别性能瓶颈，并分析系统负载变化趋势。

- **URL**: `/api/analytics/dashboard/tps-and-response-time/`
- **方法**: GET
- **权限要求**: 无特殊权限要求

## 功能需求

1. 提供指定时间段内的TPS和平均响应时间的时间序列数据
2. 支持不同的时间段选择，包括5分钟、1小时、1天和1周
3. 根据不同的时间段，提供不同粒度的数据点（如5分钟提供每秒数据，1周提供每10分钟数据）
4. 提供整体统计摘要，包括平均TPS、平均响应时间和总请求数

## 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| time_period | String | 否 | 时间段，可选值：'5m'(5分钟), '1h'(1小时), '1d'(1天), '1w'(1周)，默认为'5m' | 1h |
| system_type | String | 否 | 系统类型，可选值为api(默认)、oss、boss | boss |

## 响应结果

### 成功响应

**状态码**: 200 OK

**响应体**:

```json
{
  "time_series": [
    {
      "timestamp": "10:15:00",
      "tps": 12.34,
      "avg_response_time": 156.78
    },
    {
      "timestamp": "10:15:01",
      "tps": 15.67,
      "avg_response_time": 145.23
    },
    // ... 更多时间点数据
  ],
  "summary": {
    "avg_tps": 14.56,
    "avg_response_time": 152.45,
    "time_period": "5m",
    "total_requests": 4368
  }
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
| ----- | ---- | ---- |
| time_series | Array | 时间序列数据数组 |
| time_series[].timestamp | String | 时间点，格式根据time_period不同而不同 |
| time_series[].tps | Float | 该时间点的每秒事务处理量 |
| time_series[].avg_response_time | Float | 该时间点的平均响应时间（毫秒） |
| summary | Object | 统计摘要 |
| summary.avg_tps | Float | 整个时间段的平均TPS |
| summary.avg_response_time | Float | 整个时间段的平均响应时间（毫秒） |
| summary.time_period | String | 查询的时间段 |
| summary.total_requests | Integer | 时间段内的总请求数 |

### 失败响应

**状态码**: 400 Bad Request

**响应体**:

```json
{
  "error": "无效的时间段参数: 2h. 有效值为: ['5m', '1h', '1d', '1w']"
}
```

**状态码**: 500 Internal Server Error

**响应体**:

```json
{
  "error": "获取TPS和响应时间数据失败: [错误详情]"
}
```

## 实现细节

### 数据来源

该接口从 Elasticsearch 中获取数据，使用的索引为系统操作日志索引（`tgtweb_apioperatelog`）。

### 时间段和数据粒度

根据不同的时间段参数，接口提供不同粒度的数据：

1. **5分钟 (5m)**:
   - 时间范围：过去5分钟
   - 数据粒度：每秒1个数据点
   - 总数据点：300个
   - 时间格式：HH:mm:ss

2. **1小时 (1h)**:
   - 时间范围：过去1小时
   - 数据粒度：每分钟1个数据点
   - 总数据点：60个
   - 时间格式：HH:mm

3. **1天 (1d)**:
   - 时间范围：过去1天
   - 数据粒度：每5分钟1个数据点
   - 总数据点：288个
   - 时间格式：MM-dd HH:mm

4. **1周 (1w)**:
   - 时间范围：过去1周
   - 数据粒度：每10分钟1个数据点
   - 总数据点：1008个
   - 时间格式：MM-dd HH:mm

### TPS计算逻辑

TPS（每秒事务处理量）的计算方式根据数据粒度不同而不同：

1. **每秒数据点 (5m)**:
   - TPS = 该秒的请求数

2. **每分钟数据点 (1h)**:
   - TPS = 该分钟的请求数 / 60

3. **每5分钟数据点 (1d)**:
   - TPS = 该5分钟的请求数 / 300

4. **每10分钟数据点 (1w)**:
   - TPS = 该10分钟的请求数 / 600

为了避免没有数据的情况，如果有请求但计算结果接近0，会设置最小值为0.01。

### 平均响应时间计算

平均响应时间是通过 Elasticsearch 的 `avg` 聚合计算每个时间段内所有请求的平均 `cost` 值。

### 数据填充

为了生成完整的时间序列，接口会创建所有时间点的数据结构，然后用实际查询到的数据填充。对于没有数据的时间点，TPS和平均响应时间都设置为0。

## 使用示例

### 示例1: 获取过去5分钟的TPS和响应时间数据

**请求**:
```
GET /api/analytics/dashboard/tps-and-response-time/
```

或

```
GET /api/analytics/dashboard/tps-and-response-time/?time_period=5m
```

**响应**:
```json
{
  "time_series": [
    {
      "timestamp": "10:15:00",
      "tps": 12.34,
      "avg_response_time": 156.78
    },
    {
      "timestamp": "10:15:01",
      "tps": 15.67,
      "avg_response_time": 145.23
    },
    // ... 更多时间点数据
  ],
  "summary": {
    "avg_tps": 14.56,
    "avg_response_time": 152.45,
    "time_period": "5m",
    "total_requests": 4368
  }
}
```

### 示例2: 获取过去1小时的TPS和响应时间数据

**请求**:
```
GET /api/analytics/dashboard/tps-and-response-time/?time_period=1h
```

**响应**:
```json
{
  "time_series": [
    {
      "timestamp": "09:15",
      "tps": 10.25,
      "avg_response_time": 165.32
    },
    {
      "timestamp": "09:16",
      "tps": 11.42,
      "avg_response_time": 158.76
    },
    // ... 更多时间点数据
  ],
  "summary": {
    "avg_tps": 12.18,
    "avg_response_time": 160.34,
    "time_period": "1h",
    "total_requests": 43848
  }
}
```

### 示例3: 获取过去1天的TPS和响应时间数据

**请求**:
```
GET /api/analytics/dashboard/tps-and-response-time/?time_period=1d
```

**响应**:
```json
{
  "time_series": [
    {
      "timestamp": "04-24 09:15",
      "tps": 8.75,
      "avg_response_time": 172.45
    },
    {
      "timestamp": "04-24 09:20",
      "tps": 9.32,
      "avg_response_time": 168.91
    },
    // ... 更多时间点数据
  ],
  "summary": {
    "avg_tps": 9.65,
    "avg_response_time": 165.78,
    "time_period": "1d",
    "total_requests": 834624
  }
}
```

### 示例4: 获取过去1周的TPS和响应时间数据

**请求**:
```
GET /api/analytics/dashboard/tps-and-response-time/?time_period=1w
```

**响应**:
```json
{
  "time_series": [
    {
      "timestamp": "04-17 09:10",
      "tps": 7.85,
      "avg_response_time": 180.23
    },
    {
      "timestamp": "04-17 09:20",
      "tps": 8.12,
      "avg_response_time": 175.67
    },
    // ... 更多时间点数据
  ],
  "summary": {
    "avg_tps": 8.45,
    "avg_response_time": 170.34,
    "time_period": "1w",
    "total_requests": 5080320
  }
}
```

## 相关接口

- `/api/analytics/dashboard/overview/` - 获取系统概览数据
- `/api/analytics/dashboard/interface-tps-and-records/` - 获取按接口分组的TPS和响应时间数据
- `/api/analytics/dashboard/avg-response-time/` - 获取平均响应时间数据

## 注意事项

1. 不同时间段的数据粒度不同，请根据需要选择合适的时间段
2. 对于没有数据的时间点，TPS和平均响应时间都为0
3. 时间戳格式根据时间段不同而不同，请注意解析
4. 所有数值均保留两位小数
