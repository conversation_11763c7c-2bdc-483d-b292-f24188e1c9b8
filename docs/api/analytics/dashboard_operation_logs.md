# 操作日志接口

## 接口概述

该接口提供系统操作日志的明细数据，支持按时间范围、用户、IP地址、操作结果等多种条件进行过滤。这些数据可以帮助用户查看系统的操作历史，进行审计和故障排查。

- **URL**: `/api/analytics/dashboard/operation-logs/`
- **方法**: GET
- **权限要求**: 无特殊权限要求

## 功能需求

1. 提供系统操作日志的明细数据，包括操作时间、操作类型、操作结果、操作用户等信息
2. 支持按时间范围过滤，默认为当天数据
3. 支持按用户ID、IP地址、操作结果、操作类型、请求路径等条件过滤
4. 支持分页查询，默认每页显示10条记录
5. 当没有数据时，提供友好的提示信息和建议的时间范围

## 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| start_time | String | 否 | 开始时间，格式：YYYY-MM-DD HH:MM:SS 或 YYYY-MM-DD，默认为当天零点 | 2025-04-24 00:00:00 |
| end_time | String | 否 | 结束时间，格式：YYYY-MM-DD HH:MM:SS 或 YYYY-MM-DD，默认为当前时间 | 2025-04-24 23:59:59 |
| page | Integer | 否 | 当前页码，从1开始，默认为1 | 2 |
| page_size | Integer | 否 | 每页显示的记录数量，默认为10，最大为100 | 20 |
| requestPath | String | 否 | 请求路径过滤条件 | /api/users/ |
| accountId | String | 否 | 用户ID过滤条件 | admin |
| ipAddress | String | 否 | IP地址过滤条件 | ************* |
| result | Integer | 否 | 操作结果过滤条件，0表示失败，1表示成功 | 1 |
| operateType | String | 否 | 操作类型过滤条件 | GET |
| system_type | String | 否 | 系统类型，可选值为api(默认)、oss、boss | boss |

## 响应结果

### 成功响应

**状态码**: 200 OK

**响应体**:

```json
{
  "logs": [
    {
      "createTime": "2025-04-24 10:15:23",
      "cost": 145.67,
      "ipAddress": "*************",
      "operateType": "GET",
      "accountId": "admin",
      "result": 1,
      "requestPath": "/api/users/",
      "className": "UserController",
      "methodName": "getUsers",
      "methodParam": "{\"page\":1,\"page_size\":10}",
      "methodReturn": "{\"data\":[...],\"pagination\":{...}}"
    },
    // ... 更多日志记录
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_count": 1250,
    "total_pages": 125
  },
  "time_range": {
    "start_time": "2025-04-24 00:00:00",
    "end_time": "2025-04-24 23:59:59"
  }
}
```

**当没有数据时的响应**:

```json
{
  "logs": [],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_count": 0,
    "total_pages": 0
  },
  "time_range": {
    "start_time": "2025-04-24 00:00:00",
    "end_time": "2025-04-24 23:59:59"
  },
  "message": "没有找到符合条件的数据。请检查时间范围和过滤条件。",
  "suggested_time_ranges": {
    "today": {
      "start_time": "2025-04-24 00:00:00",
      "end_time": "2025-04-24 10:15:23"
    },
    "last_hour": {
      "start_time": "2025-04-24 09:15:23",
      "end_time": "2025-04-24 10:15:23"
    },
    "last_24_hours": {
      "start_time": "2025-04-23 10:15:23",
      "end_time": "2025-04-24 10:15:23"
    }
  }
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
| ----- | ---- | ---- |
| logs | Array | 日志记录数组 |
| logs[].createTime | String | 操作时间 |
| logs[].cost | Float | 操作耗时（毫秒） |
| logs[].ipAddress | String | 操作者IP地址 |
| logs[].operateType | String | 操作类型（GET、POST等） |
| logs[].accountId | String | 操作者账号ID |
| logs[].result | Integer | 操作结果，1表示成功，0表示失败 |
| logs[].requestPath | String | 请求路径 |
| logs[].className | String | 类名 |
| logs[].methodName | String | 方法名 |
| logs[].methodParam | String | 方法参数（JSON格式） |
| logs[].methodReturn | String | 方法返回值（JSON格式） |
| pagination | Object | 分页信息 |
| pagination.page | Integer | 当前页码 |
| pagination.page_size | Integer | 每页显示的记录数量 |
| pagination.total_count | Integer | 总记录数 |
| pagination.total_pages | Integer | 总页数 |
| time_range | Object | 时间范围信息 |
| time_range.start_time | String | 开始时间 |
| time_range.end_time | String | 结束时间 |
| message | String | 提示信息（当没有数据时） |
| suggested_time_ranges | Object | 建议的时间范围（当没有数据时） |
| suggested_time_ranges.today | Object | 今天的时间范围 |
| suggested_time_ranges.last_hour | Object | 过去1小时的时间范围 |
| suggested_time_ranges.last_24_hours | Object | 过去24小时的时间范围 |

### 失败响应

**状态码**: 500 Internal Server Error

**响应体**:

```json
{
  "error": "获取操作日志数据失败: [错误详情]",
  "details": "[详细错误信息]",
  "time_range": {
    "start_time": "2025-04-24 00:00:00",
    "end_time": "2025-04-24 23:59:59"
  }
}
```

## 实现细节

### 数据来源

该接口从 Elasticsearch 中获取数据，使用的索引为系统操作日志索引（`tgtweb_apioperatelog`）。

### 时间处理

1. **默认时间范围**：
   - 如果没有指定开始时间，则默认为当天零点
   - 如果没有指定结束时间，则默认为当前时间

2. **时间格式支持**：
   - 支持 `YYYY-MM-DD HH:MM:SS` 和 `YYYY-MM-DD` 两种格式
   - 自动处理URL编码的空格（`+`）

3. **时间验证**：
   - 确保开始时间不晚于结束时间
   - 如果开始时间过早（早于2020年），会发出警告

### 分页处理

1. **常规分页**：
   - 对于小于10000条记录的查询，使用标准的 Elasticsearch from/size 分页
   - 按时间降序排序，最新的记录排在前面

2. **深度分页**：
   - 对于超过10000条记录的查询，使用 Search After 方法进行深度分页
   - 这种方法可以避免 Elasticsearch 的深度分页限制
   - 实现了批量获取和偏移量计算，以支持任意页码的查询

### 过滤条件

支持以下过滤条件：

1. **时间范围**：通过 `start_time` 和 `end_time` 参数指定
2. **请求路径**：通过 `requestPath` 参数指定
3. **用户ID**：通过 `accountId` 参数指定
4. **IP地址**：通过 `ipAddress` 参数指定
5. **操作结果**：通过 `result` 参数指定，1表示成功，0表示失败
6. **操作类型**：通过 `operateType` 参数指定，如GET、POST等

## 使用示例

### 示例1: 获取当天的操作日志

**请求**:
```
GET /api/analytics/dashboard/operation-logs/
```

**响应**:
```json
{
  "logs": [
    {
      "createTime": "2025-04-24 10:15:23",
      "cost": 145.67,
      "ipAddress": "*************",
      "operateType": "GET",
      "accountId": "admin",
      "result": 1,
      "requestPath": "/api/users/",
      "className": "UserController",
      "methodName": "getUsers",
      "methodParam": "{\"page\":1,\"page_size\":10}",
      "methodReturn": "{\"data\":[...],\"pagination\":{...}}"
    },
    // ... 更多日志记录
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_count": 1250,
    "total_pages": 125
  },
  "time_range": {
    "start_time": "2025-04-24 00:00:00",
    "end_time": "2025-04-24 10:15:23"
  }
}
```

### 示例2: 获取指定时间范围内特定用户的操作日志

**请求**:
```
GET /api/analytics/dashboard/operation-logs/?start_time=2025-04-23+00:00:00&end_time=2025-04-24+23:59:59&accountId=admin&page=2&page_size=20
```

**响应**:
```json
{
  "logs": [
    // ... 20条日志记录
  ],
  "pagination": {
    "page": 2,
    "page_size": 20,
    "total_count": 350,
    "total_pages": 18
  },
  "time_range": {
    "start_time": "2025-04-23 00:00:00",
    "end_time": "2025-04-24 23:59:59"
  }
}
```

### 示例3: 获取特定接口的失败操作日志

**请求**:
```
GET /api/analytics/dashboard/operation-logs/?requestPath=/api/users/&result=0
```

**响应**:
```json
{
  "logs": [
    {
      "createTime": "2025-04-24 09:45:12",
      "cost": 234.56,
      "ipAddress": "*************",
      "operateType": "POST",
      "accountId": "user1",
      "result": 0,
      "requestPath": "/api/users/",
      "className": "UserController",
      "methodName": "createUser",
      "methodParam": "{\"username\":\"test\",\"email\":\"<EMAIL>\"}",
      "methodReturn": "{\"error\":\"Username already exists\"}"
    },
    // ... 更多日志记录
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_count": 45,
    "total_pages": 5
  },
  "time_range": {
    "start_time": "2025-04-24 00:00:00",
    "end_time": "2025-04-24 10:15:23"
  }
}
```

## 相关接口

- `/api/analytics/dashboard/overview/` - 获取系统概览数据
- `/api/analytics/log-details/` - 获取日志记录明细
- `/api/analytics/dashboard/interface-tps-and-records/` - 获取按接口分组的TPS和响应时间数据

## 注意事项

1. 对于大量数据的查询，建议使用时间范围和其他过滤条件来缩小查询范围
2. 对于超过10000条记录的查询，系统会自动使用深度分页方法，可能会稍微影响查询性能
3. 时间参数支持两种格式：`YYYY-MM-DD HH:MM:SS` 和 `YYYY-MM-DD`
4. 当没有数据时，系统会提供建议的时间范围，可以帮助用户调整查询条件
5. 操作结果参数 `result` 的值为 1 表示成功，0 表示失败
