# 首页仪表盘概览接口

## 接口概述

该接口提供系统首页仪表盘的概览数据，包括活跃用户数、功能调用次数、平均响应时间和错误率等关键指标。这些指标可以帮助用户快速了解系统的整体运行状况和用户活跃情况。

- **URL**: `/api/analytics/dashboard/overview/`
- **方法**: GET
- **权限要求**: 无特殊权限要求

## 功能需求

1. 提供系统当前的活跃用户数，以及与上一个相同时间段的环比变化率
2. 提供系统当前的功能调用次数，以及与上一个相同时间段的环比变化率
3. 提供系统当前的平均响应时间，以及与上一个相同时间段的环比变化率
4. 提供系统当前的错误率，以及与上一个相同时间段的环比变化率
5. 支持通过参数指定时间范围，默认为当天（0点到当前时间）

## 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| time_range | Integer | 否 | 时间范围（小时），不提供则默认为当天0点到当前时间 | 24 |
| system_type | String | 否 | 系统类型，可选值为api(默认)、oss、boss | boss |

## 响应结果

### 成功响应

**状态码**: 200 OK

**响应体**:

```json
{
  "active_users": {
    "current": 79,
    "change_rate": 2.6
  },
  "function_calls": {
    "current": 12140811,
    "change_rate": -6.89
  },
  "avg_response_time": {
    "current": 197.59,
    "change_rate": 3.62
  },
  "error_rate": {
    "current": 7.53,
    "change_rate": -4.83
  },
  "time_range": "当天"
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
| ----- | ---- | ---- |
| active_users | Object | 活跃用户数据 |
| active_users.current | Integer | 当前活跃用户数 |
| active_users.change_rate | Float | 活跃用户数环比变化率（百分比） |
| function_calls | Object | 功能调用数据 |
| function_calls.current | Integer | 当前功能调用次数 |
| function_calls.change_rate | Float | 功能调用次数环比变化率（百分比） |
| avg_response_time | Object | 平均响应时间数据 |
| avg_response_time.current | Float | 当前平均响应时间（毫秒） |
| avg_response_time.change_rate | Float | 平均响应时间环比变化率（百分比） |
| error_rate | Object | 错误率数据 |
| error_rate.current | Float | 当前错误率（百分比） |
| error_rate.change_rate | Float | 错误率环比变化率（百分比） |
| time_range | String/Integer | 时间范围，当使用默认值时为字符串"当天"，否则为整数（小时） |

### 失败响应

**状态码**: 500 Internal Server Error

**响应体**:

```json
{
  "error": "获取首页概况数据失败: [错误详情]"
}
```

## 实现细节

### 数据来源

该接口从 Elasticsearch 中获取数据，使用的索引为系统操作日志索引（`tgtweb_apioperatelog`）。

### 计算逻辑

1. **活跃用户数**:
   - 使用 Elasticsearch 的 `cardinality` 聚合计算指定时间范围内不同的 `accountId` 数量
   - 环比变化率 = (当前时间段活跃用户数 - 上一时间段活跃用户数) / 上一时间段活跃用户数 * 100%

2. **功能调用次数**:
   - 统计指定时间范围内的日志记录总数
   - 环比变化率 = (当前时间段功能调用次数 - 上一时间段功能调用次数) / 上一时间段功能调用次数 * 100%

3. **平均响应时间**:
   - 使用 Elasticsearch 的 `avg` 聚合计算指定时间范围内所有请求的平均 `cost` 值
   - 环比变化率 = (当前时间段平均响应时间 - 上一时间段平均响应时间) / 上一时间段平均响应时间 * 100%

4. **错误率**:
   - 计算指定时间范围内 `result` 字段不为 `success` 的记录占总记录的百分比
   - 环比变化率 = (当前时间段错误率 - 上一时间段错误率) / 上一时间段错误率 * 100%

### 时间范围计算

- 当前时间段: 从指定的开始时间到当前时间
- 上一时间段: 长度与当前时间段相同，结束时间为当前时间段的开始时间

## 使用示例

### 示例1: 获取当天的概览数据

**请求**:
```
GET /api/analytics/dashboard/overview/
```

**响应**:
```json
{
  "active_users": {
    "current": 79,
    "change_rate": 2.6
  },
  "function_calls": {
    "current": 12140811,
    "change_rate": -6.89
  },
  "avg_response_time": {
    "current": 197.59,
    "change_rate": 3.62
  },
  "error_rate": {
    "current": 7.53,
    "change_rate": -4.83
  },
  "time_range": "当天"
}
```

### 示例2: 获取过去24小时的概览数据

**请求**:
```
GET /api/analytics/dashboard/overview/?time_range=24
```

**响应**:
```json
{
  "active_users": {
    "current": 156,
    "change_rate": 5.4
  },
  "function_calls": {
    "current": 24567890,
    "change_rate": -2.34
  },
  "avg_response_time": {
    "current": 205.67,
    "change_rate": 1.23
  },
  "error_rate": {
    "current": 6.78,
    "change_rate": -3.45
  },
  "time_range": 24
}
```

## 相关接口

- `/api/analytics/dashboard/active-users/` - 获取更详细的活跃用户数据
- `/api/analytics/dashboard/function-calls/` - 获取更详细的功能调用数据
- `/api/analytics/dashboard/avg-response-time/` - 获取更详细的平均响应时间数据
- `/api/analytics/dashboard/error-rate/` - 获取更详细的错误率数据

## 注意事项

1. 当没有上一时间段的数据时，环比变化率将为0
2. 当指定的时间范围内没有数据时，各指标的当前值将为0
3. 环比变化率为正值表示增长，负值表示下降
4. 所有百分比值均保留两位小数
