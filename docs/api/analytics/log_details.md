# 日志记录明细接口

## 接口概述

该接口提供系统操作日志的详细查询功能，支持按账号ID、请求路径、时间范围、操作类型、结果状态等多种条件进行精确过滤。与仪表盘操作日志接口相比，该接口提供了更多的过滤条件和更详细的日志信息，适用于深入分析和故障排查场景。

- **URL**: `/api/analytics/log-details/`
- **方法**: GET
- **权限要求**: 无特殊权限要求

## 功能需求

1. 提供系统操作日志的详细查询功能，返回符合条件的日志记录
2. 支持按账号ID、请求路径、时间范围、操作类型、结果状态等多种条件进行过滤
3. 支持按最小耗时进行过滤，便于查找性能问题
4. 支持分页查询，默认每页显示10条记录
5. 提供查询结果的总数和分页信息
6. 对于大量数据的查询，提供友好的提示和建议

## 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| account_id | String | 否 | 账号ID，对应ES中的accountId字段 | admin |
| request_path | String | 否 | 请求路径，对应ES中的requestPath字段，支持模糊匹配 | /api/users |
| create_time_start | String | 否 | 创建时间开始，格式：YYYY-MM-DD HH:MM:SS | 2023-04-24 00:00:00 |
| create_time_end | String | 否 | 创建时间结束，格式：YYYY-MM-DD HH:MM:SS | 2023-04-24 23:59:59 |
| operate_type | String | 否 | 操作类型，如GET、POST等 | GET |
| result | Integer | 否 | 结果状态，1表示成功，0表示失败 | 1 |
| min_cost | Integer | 否 | 最小耗时（毫秒），只返回耗时大于等于此值的记录 | 500 |
| system_type | String | 否 | 系统类型，可选值为api(默认)、oss、boss | boss |
| page | Integer | 否 | 当前页码，从1开始，默认为1 | 2 |
| page_size | Integer | 否 | 每页显示的记录数量，默认为10 | 20 |

## 响应结果

### 成功响应

**状态码**: 200 OK

**响应体**:

```json
{
  "total": 1250,
  "page": 1,
  "page_size": 10,
  "total_pages": 125,
  "system_type": "api",
  "results": [
    {
      "id": "XYZ123456789",
      "createTime": "2023-04-24 10:15:23",
      "cost": 145.67,
      "ipAddress": "*************",
      "operateType": "GET",
      "accountId": "admin",
      "result": 1,
      "requestPath": "/api/users/",
      "className": "UserController",
      "methodName": "getUsers",
      "methodParam": "{\"page\":1,\"page_size\":10}",
      "methodReturn": "{\"data\":[...],\"pagination\":{...}}"
    },
    // ... 更多日志记录
  ]
}
```

**当查询结果超过限制时的响应**:

```json
{
  "total": 12500,
  "page": 1,
  "page_size": 10,
  "total_pages": 1250,
  "results": [
    // ... 日志记录
  ],
  "warning": "查询结果总数为12500条，超过10000条限制，只能访问前1000页的数据",
  "max_size": 10000,
  "max_page": 1000
}
```

**当尝试访问超过限制的页面时的响应**:

```json
{
  "error": "访问的页面超过10000条数据限制，请添加更多搜索条件缩小查询范围或访问前1000页的数据。\n\n您可以尝试以下方法：\n1. 添加时间范围限制\n2. 指定特定的账号ID\n3. 指定特定的请求路径\n4. 指定特定的操作类型\n5. 指定特定的结果状态",
  "total": 12500,
  "max_size": 10000,
  "max_page": 1000,
  "results": []
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
| ----- | ---- | ---- |
| total | Integer | 符合条件的记录总数 |
| page | Integer | 当前页码 |
| page_size | Integer | 每页记录数 |
| total_pages | Integer | 总页数 |
| results | Array | 日志记录数组 |
| results[].id | String | 记录的唯一标识，来自Elasticsearch的_id字段 |
| results[].createTime | String | 创建时间，格式：YYYY-MM-DD HH:MM:SS |
| results[].cost | Float | 操作耗时（毫秒） |
| results[].ipAddress | String | IP地址 |
| results[].operateType | String | 操作类型，如GET、POST等 |
| results[].accountId | String | 账号ID |
| results[].result | Integer | 结果状态，1表示成功，0表示失败 |
| results[].requestPath | String | 请求路径 |
| results[].className | String | API类名 |
| results[].methodName | String | API方法名 |
| results[].methodParam | String | API方法参数，JSON格式 |
| results[].methodReturn | String | API方法返回值，JSON格式 |
| warning | String | 警告信息（当结果超过限制时） |
| max_size | Integer | 最大可查询记录数（当结果超过限制时） |
| max_page | Integer | 最大可访问页数（当结果超过限制时） |

### 失败响应

**状态码**: 500 Internal Server Error

**响应体**:

```json
{
  "error": "查询失败: [错误详情]"
}
```

## 实现细节

### 数据来源

该接口从 Elasticsearch 中获取数据，根据 system_type 参数使用不同的索引：

- api（默认）：使用 `tgtweb_apioperatelog` 索引
- oss：使用 `tgtweb_ossoperatelog` 索引
- boss：使用 `tgtweb_operatelog` 索引

### 查询条件处理

1. **账号ID过滤**：
   - 使用精确匹配（term查询）过滤accountId字段

2. **请求路径过滤**：
   - 使用通配符匹配（wildcard查询）过滤requestPath字段
   - 支持部分匹配，如输入"/api/users"可以匹配"/api/users/"、"/api/users/profile"等

3. **时间范围过滤**：
   - 使用范围查询（range查询）过滤createTime字段
   - 支持指定开始时间、结束时间或两者都指定

4. **操作类型过滤**：
   - 使用精确匹配（term查询）过滤operateType字段

5. **结果状态过滤**：
   - 使用精确匹配（term查询）过滤result字段

6. **最小耗时过滤**：
   - 使用范围查询（range查询）过滤cost字段
   - 只返回耗时大于等于指定值的记录

### 分页和限制

1. **标准分页**：
   - 使用from/size参数实现分页
   - 默认每页显示10条记录

2. **查询限制**：
   - 为了保护系统性能，限制最多返回10000条记录
   - 当查询结果超过限制时，会在响应中添加警告信息
   - 当尝试访问超过限制的页面时，会返回错误提示和建议

3. **排序**：
   - 默认按createTime字段降序排序，最新的记录排在前面

### 错误处理和重试

1. **重试机制**：
   - 查询执行失败时，会自动重试最多3次
   - 每次重试间隔2秒

2. **时间格式化**：
   - 自动将ISO格式的时间字符串转换为更易读的格式（YYYY-MM-DD HH:MM:SS）

## 使用示例

### 示例1: 基本查询

**请求**:
```
GET /api/analytics/log-details/?page=1&page_size=10
```

**响应**:
```json
{
  "total": 1250,
  "page": 1,
  "page_size": 10,
  "total_pages": 125,
  "results": [
    {
      "createTime": "2023-04-24 10:15:23",
      "cost": 145.67,
      "ipAddress": "*************",
      "operateType": "GET",
      "accountId": "admin",
      "result": 1,
      "requestPath": "/api/users/",
      "className": "UserController",
      "methodName": "getUsers",
      "methodParam": "{\"page\":1,\"page_size\":10}",
      "methodReturn": "{\"data\":[...],\"pagination\":{...}}"
    },
    // ... 更多日志记录
  ]
}
```

### 示例2: 按时间范围和账号ID过滤

**请求**:
```
GET /api/analytics/log-details/?create_time_start=2023-04-24%2000:00:00&create_time_end=2023-04-24%2023:59:59&account_id=admin
```

**响应**:
```json
{
  "total": 456,
  "page": 1,
  "page_size": 10,
  "total_pages": 46,
  "results": [
    // ... 日志记录
  ]
}
```

### 示例3: 查询高耗时操作

**请求**:
```
GET /api/analytics/log-details/?min_cost=1000&operate_type=POST
```

**响应**:
```json
{
  "total": 25,
  "page": 1,
  "page_size": 10,
  "total_pages": 3,
  "results": [
    {
      "createTime": "2023-04-24 09:45:12",
      "cost": 2345.67,
      "ipAddress": "*************",
      "operateType": "POST",
      "accountId": "user1",
      "result": 1,
      "requestPath": "/api/reports/generate/",
      "className": "ReportController",
      "methodName": "generateReport",
      "methodParam": "{\"report_type\":\"monthly\",\"date\":\"2023-04\"}",
      "methodReturn": "{\"status\":\"success\",\"report_id\":12345}"
    },
    // ... 更多日志记录
  ]
}
```

## 相关接口

- `/api/analytics/dashboard/operation-logs/` - 获取仪表盘操作日志数据
- `/api/analytics/dashboard/feature-operation-analysis/` - 获取功能操作分析数据
- `/api/analytics/slow-functions/` - 获取耗时功能排行
- `/api/analytics/access-stats/` - 获取访问统计分析

## 注意事项

1. 为了保护系统性能，查询结果最多返回10000条记录
2. 当查询结果较多时，建议添加更多过滤条件缩小查询范围
3. 时间范围过滤是减少查询结果的最有效方式
4. 请求路径过滤支持模糊匹配，可以只输入URL的一部分
5. 结果按创建时间降序排序，最新的记录排在前面
6. 对于大型系统，建议限制查询时间范围在较短的区间内（如1天或几小时）
