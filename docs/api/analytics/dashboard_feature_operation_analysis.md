# 功能操作分析接口

## 接口概述

该接口提供系统中各功能的操作分析数据，包括每个功能的调用次数、操作人数和平均操作时间等指标。这些数据可以帮助用户了解系统中哪些功能使用频率最高、哪些功能响应时间最长，从而进行有针对性的优化和改进。

- **URL**: `/api/analytics/dashboard/feature-operation-analysis/`
- **方法**: GET
- **权限要求**: 无特殊权限要求

## 功能需求

1. 提供特定日期的功能操作分析数据，包括每个功能的调用次数、操作人数和平均操作时间
2. 支持按日期过滤，默认为当天
3. 支持按请求URL和请求类型进行过滤
4. 支持分页查询，默认每页显示10条记录
5. 提供汇总统计信息，包括总功能数、总请求次数和整体平均操作时间
6. 当没有数据时，提供友好的提示信息和建议的日期范围

## 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| date | String | 否 | 查询日期，格式：YYYY-MM-DD，默认为当天 | 2023-04-23 |
| request_url | String | 否 | 请求URL过滤条件，支持模糊查询 | /api/users |
| request_type | String | 否 | 请求类型过滤条件，如GET、POST等 | GET |
| system_type | String | 否 | 系统类型，可选值为api(默认)、oss、boss | boss |
| page | Integer | 否 | 当前页码，从1开始，默认为1 | 2 |
| page_size | Integer | 否 | 每页显示的记录数量，默认为10，最大为100 | 20 |

## 响应结果

### 成功响应

**状态码**: 200 OK

**响应体**:

```json
{
  "data": [
    {
      "request_url": "/api/users/",
      "request_type": "GET",
      "request_url_name": "用户列表",
      "avg_time": 156.78,
      "total_requests": 1245,
      "user_count": 78,
      "date": "2023-04-23"
    },
    {
      "request_url": "/api/features/",
      "request_type": "GET",
      "request_url_name": "功能列表",
      "avg_time": 123.45,
      "total_requests": 987,
      "user_count": 65,
      "date": "2023-04-23"
    },
    // ... 更多功能记录
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_count": 45,
    "total_pages": 5
  },
  "summary": {
    "date": "2023-04-23",
    "total_features": 45,
    "total_requests": 12450,
    "avg_time_overall": 187.45,
    "system_type": "api"
  }
}
```

**当没有数据时的响应**:

```json
{
  "data": [],
  "summary": {
    "date": "2023-04-23",
    "total_features": 0,
    "total_requests": 0,
    "avg_time_overall": 0,
    "system_type": "api"
  },
  "message": "没有找到符合条件的数据。请检查日期。",
  "suggested_dates": {
    "today": "2023-04-24",
    "yesterday": "2023-04-23",
    "last_week": "2023-04-17"
  }
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
| ----- | ---- | ---- |
| data | Array | 功能操作分析数据数组 |
| data[].request_url | String | 请求URL |
| data[].request_type | String | 请求类型，如GET、POST等 |
| data[].request_url_name | String | 请求URL的名称，如果没有则显示URL |
| data[].avg_time | Float | 平均操作时间（毫秒） |
| data[].total_requests | Integer | 总请求次数 |
| data[].user_count | Integer | 操作人数（不同用户ID的数量） |
| data[].date | String | 统计日期，格式：YYYY-MM-DD |
| pagination | Object | 分页信息 |
| pagination.page | Integer | 当前页码 |
| pagination.page_size | Integer | 每页记录数 |
| pagination.total_count | Integer | 总记录数 |
| pagination.total_pages | Integer | 总页数 |
| summary | Object | 汇总统计信息 |
| summary.date | String | 统计日期，格式：YYYY-MM-DD |
| summary.total_features | Integer | 总功能数 |
| summary.total_requests | Integer | 总请求次数 |
| summary.avg_time_overall | Float | 整体平均操作时间（毫秒） |
| message | String | 提示信息（当没有数据时） |
| suggested_dates | Object | 建议的日期范围（当没有数据时） |
| suggested_dates.today | String | 今天的日期 |
| suggested_dates.yesterday | String | 昨天的日期 |
| suggested_dates.last_week | String | 一周前的日期 |

### 失败响应

**状态码**: 400 Bad Request

**响应体**:

```json
{
  "error": "无效的日期格式: 2023-04-32. 正确格式为: YYYY-MM-DD"
}
```

**状态码**: 500 Internal Server Error

**响应体**:

```json
{
  "error": "获取功能操作分析数据失败: [错误详情]",
  "details": "[详细错误信息]"
}
```

## 实现细节

### 数据来源

该接口从数据库表 `tgt_api_user_operate_analysis_day` 中获取数据，该表存储了按日期、请求URL和请求类型汇总的操作统计数据。

### 日期处理

1. **默认日期**：
   - 如果没有指定日期，则默认为当天
   - 如果指定的日期格式不正确，会自动使用当天日期

2. **日期格式**：
   - 支持 `YYYY-MM-DD` 格式
   - 会验证日期的有效性

### URL过滤处理

1. **标准化URL**：
   - 系统会自动标准化URL，移除末尾的斜杠
   - 同时支持匹配原始URL和带斜杠的URL

2. **模糊查询**：
   - 支持URL的模糊查询，使用 `LIKE %keyword%` 方式实现
   - 可以只输入URL的一部分进行查询

### 数据计算逻辑

1. **平均操作时间**：
   - 直接从数据库表中获取预先计算好的平均操作时间
   - 单位为毫秒，保留两位小数

2. **操作人数**：
   - 使用 `COUNT(DISTINCT request_user_id)` 计算不同用户ID的数量
   - 排除空用户ID和空字符串

3. **整体平均时间**：
   - 使用加权平均方式计算，考虑了不同功能的请求次数权重
   - 计算公式：Σ(avg_time * total_requests) / Σ(total_requests)

## 使用示例

### 示例1: 获取当天的功能操作分析数据

**请求**:
```
GET /api/analytics/dashboard/feature-operation-analysis/
```

**响应**:
```json
{
  "data": [
    {
      "request_url": "/api/users/",
      "request_type": "GET",
      "request_url_name": "用户列表",
      "avg_time": 156.78,
      "total_requests": 1245,
      "user_count": 78,
      "date": "2023-04-24"
    },
    {
      "request_url": "/api/features/",
      "request_type": "GET",
      "request_url_name": "功能列表",
      "avg_time": 123.45,
      "total_requests": 987,
      "user_count": 65,
      "date": "2023-04-24"
    },
    // ... 更多功能记录
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_count": 45,
    "total_pages": 5
  },
  "summary": {
    "date": "2023-04-24",
    "total_features": 45,
    "total_requests": 12450,
    "avg_time_overall": 187.45
  }
}
```

### 示例2: 获取特定日期的功能操作分析数据

**请求**:
```
GET /api/analytics/dashboard/feature-operation-analysis/?date=2023-04-23
```

**响应**:
```json
{
  "data": [
    {
      "request_url": "/api/users/",
      "request_type": "GET",
      "request_url_name": "用户列表",
      "avg_time": 156.78,
      "total_requests": 1245,
      "user_count": 78,
      "date": "2023-04-23"
    },
    // ... 更多功能记录
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_count": 45,
    "total_pages": 5
  },
  "summary": {
    "date": "2023-04-23",
    "total_features": 45,
    "total_requests": 12450,
    "avg_time_overall": 187.45
  }
}
```

### 示例3: 按URL过滤功能操作分析数据

**请求**:
```
GET /api/analytics/dashboard/feature-operation-analysis/?request_url=/api/users
```

**响应**:
```json
{
  "data": [
    {
      "request_url": "/api/users/",
      "request_type": "GET",
      "request_url_name": "用户列表",
      "avg_time": 156.78,
      "total_requests": 1245,
      "user_count": 78,
      "date": "2023-04-24"
    },
    {
      "request_url": "/api/users/profile",
      "request_type": "GET",
      "request_url_name": "用户资料",
      "avg_time": 178.23,
      "total_requests": 856,
      "user_count": 72,
      "date": "2023-04-24"
    },
    // ... 更多包含 "/api/users" 的功能记录
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_count": 8,
    "total_pages": 1
  },
  "summary": {
    "date": "2023-04-24",
    "total_features": 8,
    "total_requests": 3560,
    "avg_time_overall": 165.34
  }
}
```

### 示例4: 分页查询

**请求**:
```
GET /api/analytics/dashboard/feature-operation-analysis/?page=2&page_size=20
```

**响应**:
```json
{
  "data": [
    // ... 20条功能记录
  ],
  "pagination": {
    "page": 2,
    "page_size": 20,
    "total_count": 45,
    "total_pages": 3
  },
  "summary": {
    "date": "2023-04-24",
    "total_features": 45,
    "total_requests": 12450,
    "avg_time_overall": 187.45
  }
}
```

## 相关接口

- `/api/analytics/dashboard/overview/` - 获取系统概览数据
- `/api/analytics/dashboard/user-behavior-analysis/` - 获取用户行为分析数据
- `/api/analytics/dashboard/feature-usage-stats/` - 获取功能使用统计数据
- `/api/analytics/dashboard/operation-logs/` - 获取操作日志明细数据

## 注意事项

1. 该接口返回的是已经汇总的统计数据，而不是原始的操作日志
2. 数据按请求次数降序排序，最常用的功能会排在前面
3. 整体平均操作时间是加权平均值，考虑了不同功能的请求次数权重
4. 如果某个功能没有用户ID信息，其操作人数可能为0
5. 当没有数据时，系统会提供建议的日期范围，可以帮助用户调整查询条件
6. 对于大量功能的系统，建议使用分页功能和过滤条件来缩小查询范围
