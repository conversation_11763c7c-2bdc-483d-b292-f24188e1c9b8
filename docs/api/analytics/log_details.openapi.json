{"openapi": "3.1.0", "info": {"title": "日志记录明细接口", "description": "该接口提供系统操作日志的详细查询功能，支持按账号ID、请求路径、时间范围、操作类型、结果状态等多种条件进行精确过滤。与仪表盘操作日志接口相比，该接口提供了更多的过滤条件和更详细的日志信息，适用于深入分析和故障排查场景。", "version": "1.0.0"}, "tags": [{"name": "analytics", "description": "分析统计相关接口"}], "paths": {"/api/analytics/log-details/": {"get": {"summary": "获取日志记录明细", "description": "提供系统操作日志的详细查询功能，支持多种过滤条件。", "tags": ["analytics"], "parameters": [{"name": "account_id", "in": "query", "description": "账号ID，对应ES中的accountId字段", "required": false, "example": "admin", "schema": {"type": "string"}}, {"name": "request_path", "in": "query", "description": "请求路径，对应ES中的requestPath字段，支持模糊匹配", "required": false, "example": "/api/users", "schema": {"type": "string"}}, {"name": "create_time_start", "in": "query", "description": "创建时间开始，格式：YYYY-MM-DD HH:MM:SS", "required": false, "example": "2023-04-24 00:00:00", "schema": {"type": "string", "format": "date-time"}}, {"name": "create_time_end", "in": "query", "description": "创建时间结束，格式：YYYY-MM-DD HH:MM:SS", "required": false, "example": "2023-04-24 23:59:59", "schema": {"type": "string", "format": "date-time"}}, {"name": "operate_type", "in": "query", "description": "操作类型，如GET、POST等", "required": false, "example": "GET", "schema": {"type": "string"}}, {"name": "result", "in": "query", "description": "结果状态，1表示成功，0表示失败", "required": false, "example": 1, "schema": {"type": "integer"}}, {"name": "min_cost", "in": "query", "description": "最小耗时（毫秒），只返回耗时大于等于此值的记录", "required": false, "example": 500, "schema": {"type": "integer"}}, {"name": "system_type", "in": "query", "description": "系统类型，可选值为api(默认)、oss、boss", "required": false, "example": "boss", "schema": {"type": "string", "enum": ["api", "oss", "boss"], "default": "api"}}, {"name": "page", "in": "query", "description": "当前页码，从1开始，默认为1", "required": false, "example": 2, "schema": {"type": "integer", "default": 1, "minimum": 1}}, {"name": "page_size", "in": "query", "description": "每页显示的记录数量，默认为10", "required": false, "example": 20, "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 100}}], "responses": {"200": {"description": "成功获取日志记录", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LogDetailsResponse"}, "examples": {"success": {"summary": "成功响应示例", "value": {"total": 1250, "page": 1, "page_size": 10, "total_pages": 125, "results": [{"createTime": "2023-04-24 10:15:23", "cost": 145.67, "ipAddress": "*************", "operateType": "GET", "accountId": "admin", "result": 1, "requestPath": "/api/users/", "className": "UserController", "methodName": "getUsers", "methodParam": "{\"page\":1,\"page_size\":10}", "methodReturn": "{\"data\":[...],\"pagination\":{...}}"}]}}, "over_limit": {"summary": "超过限制响应示例", "value": {"total": 12500, "page": 1, "page_size": 10, "total_pages": 1250, "results": [], "warning": "查询结果总数为12500条，超过10000条限制，只能访问前1000页的数据", "max_size": 10000, "max_page": 1000}}, "empty": {"summary": "无数据响应示例", "value": {"total": 0, "page": 1, "page_size": 10, "total_pages": 0, "results": []}}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"invalid_time_format": {"summary": "时间格式错误", "value": {"error": "时间格式不正确，正确格式为: YYYY-MM-DD HH:MM:SS"}}}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "查询失败: 内部服务器错误"}}}}}}}}, "components": {"schemas": {"LogDetailsResponse": {"type": "object", "required": ["total", "page", "page_size", "total_pages", "results"], "properties": {"total": {"type": "integer", "description": "符合条件的记录总数"}, "page": {"type": "integer", "description": "当前页码"}, "page_size": {"type": "integer", "description": "每页记录数"}, "total_pages": {"type": "integer", "description": "总页数"}, "results": {"type": "array", "description": "日志记录数组", "items": {"$ref": "#/components/schemas/LogDetailsItem"}}, "warning": {"type": "string", "description": "警告信息（当结果超过限制时）"}, "max_size": {"type": "integer", "description": "最大可查询记录数（当结果超过限制时）"}, "max_page": {"type": "integer", "description": "最大可访问页数（当结果超过限制时）"}}}, "LogDetailsItem": {"type": "object", "required": ["createTime", "cost", "ip<PERSON><PERSON><PERSON>", "operateType", "accountId", "result", "requestPath", "className", "methodName"], "properties": {"id": {"type": "string", "description": "记录的唯一标识，来自Elasticsearch的_id字段"}, "createTime": {"type": "string", "description": "创建时间，格式：YYYY-MM-DD HH:MM:SS"}, "cost": {"type": "number", "description": "操作耗时（毫秒）"}, "ipAddress": {"type": "string", "description": "IP地址"}, "operateType": {"type": "string", "description": "操作类型，如GET、POST等"}, "accountId": {"type": "string", "description": "账号ID"}, "result": {"type": "integer", "description": "结果状态，1表示成功，0表示失败"}, "requestPath": {"type": "string", "description": "请求路径"}, "className": {"type": "string", "description": "API类名"}, "methodName": {"type": "string", "description": "API方法名"}, "methodParam": {"type": "string", "description": "API方法参数，JSON格式"}, "methodReturn": {"type": "string", "description": "API方法返回值，JSON格式"}}}, "ErrorResponse": {"type": "object", "required": ["error"], "properties": {"error": {"type": "string", "description": "错误信息"}}}}}, "servers": [{"url": "http://localhost:8000", "description": "本地开发服务器"}]}