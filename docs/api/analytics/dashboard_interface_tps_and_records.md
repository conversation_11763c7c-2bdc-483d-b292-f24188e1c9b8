# 接口TPS和记录接口

## 接口概述

该接口提供按接口分组的TPS（每秒事务处理量）和响应时间数据，以及每个接口的最近调用记录。这些数据可以帮助用户监控各个接口的性能，识别高负载接口和性能瓶颈，并分析接口调用模式。

- **URL**: `/api/analytics/dashboard/interface-tps-and-records/`
- **方法**: GET
- **权限要求**: 无特殊权限要求

## 功能需求

1. 提供指定时间段内按接口分组的TPS和响应时间数据
2. 提供每个接口的最近调用记录（默认5条）
3. 支持不同的时间段选择，包括5分钟、1小时、1天和1周
4. 支持分页功能，以便处理大量接口数据
5. 支持过滤特定接口的数据
6. 可选择是否包含完整的时间序列数据

## 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| time_period | String | 否 | 时间段，可选值：'5m'(5分钟), '1h'(1小时), '1d'(1天), '1w'(1周)，默认为'5m' | 1h |
| page | Integer | 否 | 当前页码，从1开始，默认为1 | 2 |
| page_size | Integer | 否 | 每页显示的接口数量，默认为10 | 20 |
| filter_url | String | 否 | 过滤特定的接口URL，如果提供则只返回该URL的数据 | /api/users/ |
| system_type | String | 否 | 系统类型，可选值为api(默认)、oss、boss | boss |
| include_time_series | Boolean | 否 | 是否包含完整的时间序列数据，默认为true | false |

## 响应结果

### 成功响应

**状态码**: 200 OK

**响应体**:

```json
{
  "interfaces": [
    {
      "interface_url": "/api/users/",
      "request_count": 1250,
      "tps": 4.17,
      "avg_response_time": 156.78,
      "max_response_time": 532.45,
      "min_response_time": 45.23,
      "recent_records": [
        {
          "request_time": "2025-04-24 10:15:23",
          "cost_time": 145.67,
          "operate_result": "success",
          "user_id": "admin",
          "user_name": "UserController",
          "ip": "*************"
        },
        // ... 更多记录
      ],
      "time_series": [
        {
          "timestamp": "10:15:00",
          "tps": 3.45,
          "avg_response_time": 165.32
        },
        // ... 更多时间点数据
      ]
    },
    // ... 更多接口数据
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_interfaces": 45,
    "total_pages": 5
  },
  "summary": {
    "time_period": "5m",
    "start_time": "2025-04-24 10:10:00",
    "end_time": "2025-04-24 10:15:00"
  }
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
| ----- | ---- | ---- |
| interfaces | Array | 接口数据数组，按TPS降序排序 |
| interfaces[].interface_url | String | 接口URL |
| interfaces[].request_count | Integer | 时间段内的请求总数 |
| interfaces[].tps | Float | 每秒事务处理量 |
| interfaces[].avg_response_time | Float | 平均响应时间（毫秒） |
| interfaces[].max_response_time | Float | 最大响应时间（毫秒） |
| interfaces[].min_response_time | Float | 最小响应时间（毫秒） |
| interfaces[].recent_records | Array | 最近的调用记录数组 |
| interfaces[].recent_records[].request_time | String | 请求时间 |
| interfaces[].recent_records[].cost_time | Float | 耗时（毫秒） |
| interfaces[].recent_records[].operate_result | String | 操作结果 |
| interfaces[].recent_records[].user_id | String | 用户ID |
| interfaces[].recent_records[].user_name | String | 用户名/类名 |
| interfaces[].recent_records[].ip | String | IP地址 |
| interfaces[].time_series | Array | 时间序列数据数组（当include_time_series=true时） |
| interfaces[].time_series[].timestamp | String | 时间点 |
| interfaces[].time_series[].tps | Float | 该时间点的TPS |
| interfaces[].time_series[].avg_response_time | Float | 该时间点的平均响应时间（毫秒） |
| pagination | Object | 分页信息 |
| pagination.page | Integer | 当前页码 |
| pagination.page_size | Integer | 每页显示的接口数量 |
| pagination.total_interfaces | Integer | 总接口数 |
| pagination.total_pages | Integer | 总页数 |
| summary | Object | 统计摘要 |
| summary.time_period | String | 查询的时间段 |
| summary.start_time | String | 开始时间 |
| summary.end_time | String | 结束时间 |

### 失败响应

**状态码**: 400 Bad Request

**响应体**:

```json
{
  "error": "无效的时间段参数: 2h. 有效值为: ['5m', '1h', '1d', '1w']"
}
```

**状态码**: 500 Internal Server Error

**响应体**:

```json
{
  "error": "获取接口TPS和记录数据失败: [错误详情]"
}
```

## 实现细节

### 数据来源

该接口从 Elasticsearch 中获取数据，使用的索引为系统操作日志索引（`tgtweb_apioperatelog`）。

### 时间段和数据粒度

根据不同的时间段参数，接口提供不同粒度的时间序列数据：

1. **5分钟 (5m)**:
   - 时间范围：过去5分钟
   - 数据粒度：每秒1个数据点
   - 总数据点：300个
   - 时间格式：HH:mm:ss

2. **1小时 (1h)**:
   - 时间范围：过去1小时
   - 数据粒度：每分钟1个数据点
   - 总数据点：60个
   - 时间格式：HH:mm

3. **1天 (1d)**:
   - 时间范围：过去1天
   - 数据粒度：每5分钟1个数据点
   - 总数据点：288个
   - 时间格式：MM-dd HH:mm

4. **1周 (1w)**:
   - 时间范围：过去1周
   - 数据粒度：每10分钟1个数据点
   - 总数据点：1008个
   - 时间格式：MM-dd HH:mm

### TPS计算逻辑

TPS（每秒事务处理量）的计算方式根据数据粒度不同而不同：

1. **每秒数据点 (5m)**:
   - TPS = 该秒的请求数

2. **每分钟数据点 (1h)**:
   - TPS = 该分钟的请求数 / 60

3. **每5分钟数据点 (1d)**:
   - TPS = 该5分钟的请求数 / 300

4. **每10分钟数据点 (1w)**:
   - TPS = 该10分钟的请求数 / 600

为了避免没有数据的情况，如果有请求但计算结果接近0，会设置最小值为0.01。

### 接口分组和排序

接口数据按照以下步骤处理：

1. 使用 Elasticsearch 的 `terms` 聚合按 `requestPath` 字段分组
2. 对每个接口组计算请求数、TPS、平均响应时间、最大响应时间和最小响应时间
3. 获取每个接口的最近5条调用记录
4. 如果需要，获取每个接口的时间序列数据
5. 按TPS降序排序接口列表
6. 应用分页逻辑，返回当前页的接口数据

### 分页逻辑

分页逻辑如下：

1. 当没有提供 `filter_url` 参数时，对所有接口数据进行分页
2. 当提供了 `filter_url` 参数时，不进行分页，只返回该接口的数据
3. 分页信息包括当前页码、每页显示的接口数量、总接口数和总页数

## 使用示例

### 示例1: 获取过去5分钟的所有接口数据（第1页，每页10条）

**请求**:
```
GET /api/analytics/dashboard/interface-tps-and-records/
```

或

```
GET /api/analytics/dashboard/interface-tps-and-records/?time_period=5m&page=1&page_size=10&include_time_series=true
```

**响应**:
```json
{
  "interfaces": [
    {
      "interface_url": "/api/users/",
      "request_count": 1250,
      "tps": 4.17,
      "avg_response_time": 156.78,
      "max_response_time": 532.45,
      "min_response_time": 45.23,
      "recent_records": [
        {
          "request_time": "2025-04-24 10:15:23",
          "cost_time": 145.67,
          "operate_result": "success",
          "user_id": "admin",
          "user_name": "UserController",
          "ip": "*************"
        },
        // ... 更多记录
      ],
      "time_series": [
        {
          "timestamp": "10:15:00",
          "tps": 3.45,
          "avg_response_time": 165.32
        },
        // ... 更多时间点数据
      ]
    },
    // ... 更多接口数据
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_interfaces": 45,
    "total_pages": 5
  },
  "summary": {
    "time_period": "5m",
    "start_time": "2025-04-24 10:10:00",
    "end_time": "2025-04-24 10:15:00"
  }
}
```

### 示例2: 获取过去1小时的特定接口数据，不包含时间序列

**请求**:
```
GET /api/analytics/dashboard/interface-tps-and-records/?time_period=1h&filter_url=/api/users/&include_time_series=false
```

**响应**:
```json
{
  "interfaces": [
    {
      "interface_url": "/api/users/",
      "request_count": 3560,
      "tps": 0.99,
      "avg_response_time": 162.45,
      "max_response_time": 587.32,
      "min_response_time": 42.18,
      "recent_records": [
        {
          "request_time": "2025-04-24 10:15:23",
          "cost_time": 145.67,
          "operate_result": "success",
          "user_id": "admin",
          "user_name": "UserController",
          "ip": "*************"
        },
        // ... 更多记录
      ]
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_interfaces": 1,
    "total_pages": 1
  },
  "summary": {
    "time_period": "1h",
    "start_time": "2025-04-24 09:15:00",
    "end_time": "2025-04-24 10:15:00"
  }
}
```

### 示例3: 获取过去1天的接口数据（第2页，每页20条）

**请求**:
```
GET /api/analytics/dashboard/interface-tps-and-records/?time_period=1d&page=2&page_size=20
```

**响应**:
```json
{
  "interfaces": [
    // ... 20个接口的数据
  ],
  "pagination": {
    "page": 2,
    "page_size": 20,
    "total_interfaces": 45,
    "total_pages": 3
  },
  "summary": {
    "time_period": "1d",
    "start_time": "2025-04-23 10:15:00",
    "end_time": "2025-04-24 10:15:00"
  }
}
```

## 相关接口

- `/api/analytics/dashboard/overview/` - 获取系统概览数据
- `/api/analytics/dashboard/tps-and-response-time/` - 获取整体TPS和响应时间数据
- `/api/analytics/log-details/` - 获取日志记录明细

## 注意事项

1. 当使用 `filter_url` 参数时，分页参数将被忽略，只返回该接口的数据
2. 设置 `include_time_series=false` 可以减少响应数据量，提高接口性能
3. 接口列表按TPS降序排序，可以快速识别高负载接口
4. 时间序列数据的粒度和格式根据时间段不同而不同，请注意解析
5. 所有数值均保留两位小数
