# 功能使用统计接口

## 接口概述

该接口提供系统中各功能的使用情况统计数据，包括点击率最高/最低和用户操作最多/最少的功能列表。这些数据可以帮助用户了解系统中哪些功能最受欢迎、哪些功能使用频率较低，从而进行有针对性的功能优化和用户体验改进。

- **URL**: `/api/analytics/dashboard/feature-usage-stats/`
- **方法**: GET
- **权限要求**: 无特殊权限要求

## 功能需求

1. 提供特定日期的功能使用情况统计数据
2. 返回点击率最高的功能列表（按请求次数排序）
3. 返回点击率最低的功能列表（按请求次数排序）
4. 返回用户操作最多的功能列表（按不同用户数量排序）
5. 返回用户操作最少的功能列表（按不同用户数量排序）
6. 默认使用昨天的数据，支持指定日期查询

## 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| date | String | 否 | 查询日期，格式：YYYY-MM-DD，默认为昨天 | 2023-04-23 |
| system_type | String | 否 | 系统类型，可选值为api(默认)、oss、boss | boss |

## 响应结果

### 成功响应

**状态码**: 200 OK

**响应体**:

```json
{
  "date": "2023-04-23",
  "top_clicks": [
    {
      "request_url": "/api/users/",
      "request_url_name": "用户列表",
      "request_count": 1245,
      "avg_time": 156
    },
    {
      "request_url": "/api/features/",
      "request_url_name": "功能列表",
      "request_count": 987,
      "avg_time": 123
    },
    // ... 更多功能记录（共10条）
  ],
  "bottom_clicks": [
    {
      "request_url": "/api/reports/export/",
      "request_url_name": "导出报表",
      "request_count": 5,
      "avg_time": 345
    },
    {
      "request_url": "/api/tasks/logs/",
      "request_url_name": "任务日志",
      "request_count": 8,
      "avg_time": 234
    },
    // ... 更多功能记录（共10条）
  ],
  "top_operations": [
    {
      "request_url": "/api/users/",
      "request_url_name": "用户列表",
      "user_count": 78,
      "total_count": 1245,
      "avg_response_time": 156.78
    },
    {
      "request_url": "/api/features/",
      "request_url_name": "功能列表",
      "user_count": 65,
      "total_count": 987,
      "avg_response_time": 123.45
    },
    // ... 更多功能记录（共10条）
  ],
  "bottom_operations": [
    {
      "request_url": "/api/reports/export/",
      "request_url_name": "导出报表",
      "user_count": 3,
      "total_count": 5,
      "avg_response_time": 345.67
    },
    {
      "request_url": "/api/tasks/logs/",
      "request_url_name": "任务日志",
      "user_count": 4,
      "total_count": 8,
      "avg_response_time": 234.56
    },
    // ... 更多功能记录（共10条）
  ]
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
| ----- | ---- | ---- |
| date | String | 统计日期，格式：YYYY-MM-DD |
| top_clicks | Array | 点击率最高的功能列表（按请求次数降序排序） |
| top_clicks[].request_url | String | 请求URL |
| top_clicks[].request_url_name | String | 请求URL的名称，如果没有则显示URL |
| top_clicks[].request_count | Integer | 请求次数 |
| top_clicks[].avg_time | Integer | 平均响应时间（毫秒） |
| bottom_clicks | Array | 点击率最低的功能列表（按请求次数升序排序） |
| bottom_clicks[].request_url | String | 请求URL |
| bottom_clicks[].request_url_name | String | 请求URL的名称，如果没有则显示URL |
| bottom_clicks[].request_count | Integer | 请求次数 |
| bottom_clicks[].avg_time | Integer | 平均响应时间（毫秒） |
| top_operations | Array | 用户操作最多的功能列表（按不同用户数量降序排序） |
| top_operations[].request_url | String | 请求URL |
| top_operations[].request_url_name | String | 请求URL的名称，如果没有则显示URL |
| top_operations[].user_count | Integer | 操作人数（不同用户ID的数量） |
| top_operations[].total_count | Integer | 总请求次数 |
| top_operations[].avg_response_time | Float | 平均响应时间（毫秒） |
| bottom_operations | Array | 用户操作最少的功能列表（按不同用户数量升序排序） |
| bottom_operations[].request_url | String | 请求URL |
| bottom_operations[].request_url_name | String | 请求URL的名称，如果没有则显示URL |
| bottom_operations[].user_count | Integer | 操作人数（不同用户ID的数量） |
| bottom_operations[].total_count | Integer | 总请求次数 |
| bottom_operations[].avg_response_time | Float | 平均响应时间（毫秒） |

### 失败响应

**状态码**: 400 Bad Request

**响应体**:

```json
{
  "error": "无效的日期格式: 2023-04-32. 正确格式为: YYYY-MM-DD"
}
```

或

```json
{
  "error": "日期 2023-04-23 没有数据",
  "date": "2023-04-23"
}
```

**状态码**: 500 Internal Server Error

**响应体**:

```json
{
  "error": "获取功能使用情况统计失败: [错误详情]"
}
```

## 实现细节

### 数据来源

该接口从数据库表 `tgt_api_user_operate_analysis_day` 中获取数据，该表存储了按日期、请求URL和请求类型汇总的操作统计数据。

### 日期处理

1. **默认日期**：
   - 如果没有指定日期，则默认为昨天
   - 这是因为统计数据通常在次日凌晨生成

2. **日期格式**：
   - 支持 `YYYY-MM-DD` 格式
   - 会验证日期的有效性

### 数据计算逻辑

1. **点击率最高/最低的功能**：
   - 按请求URL分组，计算每个URL的总请求次数和平均响应时间
   - 按总请求次数降序/升序排序，获取前10个结果

2. **用户操作最多/最少的功能**：
   - 按请求URL分组，计算每个URL的不同用户ID数量、总请求次数和平均响应时间
   - 按不同用户ID数量降序/升序排序，获取前10个结果

3. **URL名称处理**：
   - 如果URL有对应的名称，则使用该名称
   - 如果没有名称，则使用URL本身作为名称

## 使用示例

### 示例1: 获取昨天的功能使用情况统计

**请求**:
```
GET /api/analytics/dashboard/feature-usage-stats/
```

**响应**:
```json
{
  "date": "2023-04-23",
  "top_clicks": [
    {
      "request_url": "/api/users/",
      "request_url_name": "用户列表",
      "request_count": 1245,
      "avg_time": 156
    },
    // ... 更多功能记录
  ],
  "bottom_clicks": [
    // ... 功能记录
  ],
  "top_operations": [
    // ... 功能记录
  ],
  "bottom_operations": [
    // ... 功能记录
  ]
}
```

### 示例2: 获取特定日期的功能使用情况统计

**请求**:
```
GET /api/analytics/dashboard/feature-usage-stats/?date=2023-04-22
```

**响应**:
```json
{
  "date": "2023-04-22",
  "top_clicks": [
    {
      "request_url": "/api/users/",
      "request_url_name": "用户列表",
      "request_count": 1198,
      "avg_time": 162
    },
    // ... 更多功能记录
  ],
  "bottom_clicks": [
    // ... 功能记录
  ],
  "top_operations": [
    // ... 功能记录
  ],
  "bottom_operations": [
    // ... 功能记录
  ]
}
```

### 示例3: 查询没有数据的日期

**请求**:
```
GET /api/analytics/dashboard/feature-usage-stats/?date=2023-01-01
```

**响应**:
```json
{
  "error": "日期 2023-01-01 没有数据",
  "date": "2023-01-01"
}
```

## 相关接口

- `/api/analytics/dashboard/feature-operation-analysis/` - 获取功能操作分析数据
- `/api/analytics/dashboard/user-behavior-analysis/` - 获取用户行为分析数据
- `/api/features/usage/` - 获取功能使用情况
- `/api/features/performance/` - 获取功能性能指标

## 注意事项

1. 该接口返回的是已经汇总的统计数据，而不是原始的操作日志
2. 统计数据通常在次日凌晨生成，因此默认查询昨天的数据
3. 每个分类（top_clicks、bottom_clicks、top_operations、bottom_operations）最多返回10条记录
4. 如果某个功能没有用户ID信息，其操作人数可能为0
5. 如果指定日期没有数据，接口会返回错误信息而不是空结果
6. 平均响应时间单位为毫秒，在top_clicks和bottom_clicks中为整数，在top_operations和bottom_operations中为浮点数
