# 用户行为分析接口

## 接口概述

该接口提供特定日期和用户的行为分析数据，包括用户访问的各个接口的请求次数、平均响应时间等信息。这些数据可以帮助了解用户的使用习惯、偏好和行为模式，为产品优化和个性化推荐提供依据。

- **URL**: `/api/analytics/dashboard/user-behavior-analysis/`
- **方法**: GET
- **权限要求**: 无特殊权限要求

## 功能需求

1. 提供特定日期和用户的行为分析数据
2. 支持按日期过滤，默认为昨天
3. 支持按用户ID过滤，可选择查看所有用户或特定用户的数据
4. 支持分页查询，默认每页显示10条记录
5. 提供汇总统计信息，包括总请求次数和整体平均响应时间
6. 当没有数据时，提供友好的提示信息

## 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| date | String | 否 | 查询日期，格式：YYYY-MM-DD，默认为昨天 | 2025-04-23 |
| user_id | String | 否 | 用户ID，不提供则查询所有用户 | admin |
| request_url | String | 否 | 请求URL过滤条件，支持模糊查询 | /api/users |
| request_type | String | 否 | 请求类型过滤条件，如GET、POST等 | GET |
| system_type | String | 否 | 系统类型，可选值为api(默认)、oss、boss | boss |
| page | Integer | 否 | 当前页码，从1开始，默认为1 | 2 |
| page_size | Integer | 否 | 每页显示的记录数量，默认为10，最大为100 | 20 |

## 响应结果

### 成功响应

**状态码**: 200 OK

**响应体**:

```json
{
  "data": [
    {
      "id": 12345,
      "request_url": "/api/users/",
      "request_user_id": "admin",
      "request_url_name": "用户管理",
      "request_type": "GET",
      "avg_time": 156,
      "request_count": 45,
      "day": "2025-04-23",
      "created_time": "2025-04-24 00:05:23"
    },
    {
      "id": 12346,
      "request_url": "/api/reports/",
      "request_user_id": "admin",
      "request_url_name": "报表管理",
      "request_type": "GET",
      "avg_time": 234,
      "request_count": 32,
      "day": "2025-04-23",
      "created_time": "2025-04-24 00:05:23"
    },
    // ... 更多记录
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_count": 25,
    "total_pages": 3
  },
  "summary": {
    "date": "2025-04-23",
    "total_requests": 350,
    "avg_time_overall": 187.45,
    "user_id": "admin"
  }
}
```

**当没有数据时的响应**:

```json
{
  "data": [],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_count": 0,
    "total_pages": 0
  },
  "summary": {
    "date": "2025-04-23",
    "total_requests": 0,
    "avg_time_overall": 0,
    "user_id": "admin"
  },
  "message": "没有找到符合条件的数据。请检查日期和用户ID。"
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
| ----- | ---- | ---- |
| data | Array | 用户行为分析数据数组 |
| data[].id | Integer | 记录ID |
| data[].request_url | String | 请求URL |
| data[].request_user_id | String | 用户ID |
| data[].request_url_name | String | 请求URL名称 |
| data[].request_type | String | 请求类型 |
| data[].avg_time | Integer | 平均响应时间（毫秒） |
| data[].request_count | Integer | 请求次数 |
| data[].day | String | 统计日期 |
| data[].created_time | String | 创建时间 |
| pagination | Object | 分页信息 |
| pagination.page | Integer | 当前页码 |
| pagination.page_size | Integer | 每页显示的记录数量 |
| pagination.total_count | Integer | 总记录数 |
| pagination.total_pages | Integer | 总页数 |
| summary | Object | 汇总统计信息 |
| summary.date | String | 查询日期 |
| summary.total_requests | Integer | 总请求次数 |
| summary.avg_time_overall | Float | 整体平均响应时间（毫秒） |
| summary.user_id | String | 查询的用户ID，为null表示查询所有用户 |
| message | String | 提示信息（当没有数据时） |

### 失败响应

**状态码**: 400 Bad Request

**响应体**:

```json
{
  "error": "无效的日期格式: 2025-04-32. 正确格式为: YYYY-MM-DD"
}
```

**状态码**: 500 Internal Server Error

**响应体**:

```json
{
  "error": "获取用户行为分析数据失败: [错误详情]",
  "details": "[详细错误信息]"
}
```

## 实现细节

### 数据来源

该接口从数据库表 `tgt_api_user_operate_analysis_day` 中获取数据，该表存储了按日期和用户ID汇总的操作统计数据。

### 日期处理

1. **默认日期**：
   - 如果没有指定日期，则默认为昨天
   - 这是因为统计数据通常在次日凌晨生成

2. **日期格式**：
   - 支持 `YYYY-MM-DD` 格式
   - 会验证日期的有效性，例如不接受 `2025-02-30` 这样的无效日期

### 用户ID处理

1. **全部用户**：
   - 如果没有指定用户ID，则查询所有用户的数据
   - 此时返回的是按请求次数降序排序的接口列表

2. **特定用户**：
   - 如果指定了用户ID，则只查询该用户的数据
   - 此时返回的是该用户按请求次数降序排序的接口列表

### 数据排序

数据按照 `request_count`（请求次数）降序排序，这样最常用的接口会排在前面。

### 汇总统计计算

1. **总请求次数**：
   - 当前页所有记录的 `request_count` 之和

2. **整体平均响应时间**：
   - 加权平均值：Σ(avg_time * request_count) / Σ(request_count)
   - 这样可以考虑不同接口的请求次数权重

## 使用示例

### 示例1: 获取昨天所有用户的行为分析数据

**请求**:
```
GET /api/analytics/dashboard/user-behavior-analysis/
```

**响应**:
```json
{
  "data": [
    {
      "id": 12345,
      "request_url": "/api/users/",
      "request_user_id": null,
      "request_url_name": "用户管理",
      "request_type": "GET",
      "avg_time": 156,
      "request_count": 1245,
      "day": "2025-04-23",
      "created_time": "2025-04-24 00:05:23"
    },
    // ... 更多记录
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_count": 45,
    "total_pages": 5
  },
  "summary": {
    "date": "2025-04-23",
    "total_requests": 12450,
    "avg_time_overall": 187.45,
    "user_id": null
  }
}
```

### 示例2: 获取特定日期特定用户的行为分析数据

**请求**:
```
GET /api/analytics/dashboard/user-behavior-analysis/?date=2025-04-22&user_id=admin
```

**响应**:
```json
{
  "data": [
    {
      "id": 12346,
      "request_url": "/api/reports/",
      "request_user_id": "admin",
      "request_url_name": "报表管理",
      "request_type": "GET",
      "avg_time": 234,
      "request_count": 32,
      "day": "2025-04-22",
      "created_time": "2025-04-23 00:05:23"
    },
    // ... 更多记录
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_count": 15,
    "total_pages": 2
  },
  "summary": {
    "date": "2025-04-22",
    "total_requests": 150,
    "avg_time_overall": 205.67,
    "user_id": "admin"
  }
}
```

### 示例3: 分页查询

**请求**:
```
GET /api/analytics/dashboard/user-behavior-analysis/?page=2&page_size=20
```

**响应**:
```json
{
  "data": [
    // ... 20条记录
  ],
  "pagination": {
    "page": 2,
    "page_size": 20,
    "total_count": 45,
    "total_pages": 3
  },
  "summary": {
    "date": "2025-04-23",
    "total_requests": 8750,
    "avg_time_overall": 192.34,
    "user_id": null
  }
}
```

## 相关接口

- `/api/analytics/dashboard/overview/` - 获取系统概览数据
- `/api/analytics/dashboard/feature-operation-analysis/` - 获取功能操作分析数据
- `/api/analytics/dashboard/feature-usage-stats/` - 获取功能使用统计数据

## 注意事项

1. 该接口返回的是已经汇总的统计数据，而不是原始的操作日志
2. 统计数据通常在次日凌晨生成，因此默认查询昨天的数据
3. 整体平均响应时间是加权平均值，考虑了不同接口的请求次数权重
4. 当查询所有用户时，`request_user_id` 字段可能为 null
5. 数据按请求次数降序排序，最常用的接口会排在前面
