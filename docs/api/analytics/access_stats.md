# 访问统计分析接口

## 接口概述

该接口提供特定用户在指定时间范围内访问某个功能的时间序列统计数据，按分钟粒度进行聚合。通过这个接口，可以分析用户的访问模式、高峰期使用情况，以及特定功能的使用频率变化趋势，为用户行为分析和系统负载优化提供数据支持。

- **URL**: `/api/analytics/access-stats/`
- **方法**: GET
- **权限要求**: 无特殊权限要求

## 功能需求

1. 提供特定用户在指定时间范围内访问某个功能的分钟级统计数据
2. 支持按最小访问次数过滤，只返回访问次数大于等于指定值的记录
3. 支持分页查询，默认每页显示10条记录
4. 提供查询结果的总数和分页信息
5. 提供查询摘要，包含查询参数和结果统计
6. 当没有数据时，提供友好的提示信息

## 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
| ----- | ---- | ---- | ---- | ---- |
| start_time | String | 是 | 开始时间，格式：YYYY-MM-DD HH:MM:SS | 2023-04-24 00:00:00 |
| end_time | String | 是 | 结束时间，格式：YYYY-MM-DD HH:MM:SS | 2023-04-24 01:00:00 |
| account_id | String | 是 | 账号ID | admin |
| request_path | String | 是 | 请求路径 | /api/users/ |
| min_count | Integer | 否 | 最小访问次数，只返回访问次数大于等于此值的记录，默认为1 | 5 |
| system_type | String | 否 | 系统类型，可选值为api(默认)、oss、boss | boss |
| page | Integer | 否 | 当前页码，从1开始，默认为1 | 2 |
| page_size | Integer | 否 | 每页显示的记录数量，默认为10，最大为100 | 20 |

## 响应结果

### 成功响应

**状态码**: 200 OK

**响应体**:

```json
{
  "data": [
    {
      "timestamp": "2023-04-24 00:01:00",
      "count": 5
    },
    {
      "timestamp": "2023-04-24 00:02:00",
      "count": 8
    },
    {
      "timestamp": "2023-04-24 00:03:00",
      "count": 12
    },
    // ... 更多时间点记录
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_count": 35,
    "total_pages": 4
  },
  "summary": {
    "account_id": "admin",
    "request_path": "/api/users/",
    "start_time": "2023-04-24 00:00:00",
    "end_time": "2023-04-24 01:00:00",
    "min_count": 1,
    "total_minutes": 35
  }
}
```

**当没有数据时的响应**:

```json
{
  "data": [],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_count": 0,
    "total_pages": 0
  },
  "summary": {
    "account_id": "admin",
    "request_path": "/api/users/",
    "start_time": "2023-04-24 00:00:00",
    "end_time": "2023-04-24 01:00:00",
    "min_count": 1,
    "total_minutes": 0
  },
  "message": "没有找到符合条件的数据。请检查查询参数或调整时间范围。"
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
| ----- | ---- | ---- |
| data | Array | 访问统计数据数组 |
| data[].timestamp | String | 时间戳，格式：YYYY-MM-DD HH:MM:SS |
| data[].count | Integer | 该分钟内的访问次数 |
| pagination | Object | 分页信息 |
| pagination.page | Integer | 当前页码 |
| pagination.page_size | Integer | 每页记录数 |
| pagination.total_count | Integer | 总记录数 |
| pagination.total_pages | Integer | 总页数 |
| summary | Object | 查询摘要 |
| summary.account_id | String | 查询的账号ID |
| summary.request_path | String | 查询的请求路径 |
| summary.start_time | String | 开始时间，格式：YYYY-MM-DD HH:MM:SS |
| summary.end_time | String | 结束时间，格式：YYYY-MM-DD HH:MM:SS |
| summary.min_count | Integer | 最小访问次数 |
| summary.total_minutes | Integer | 有访问记录的分钟数 |
| message | String | 提示信息（当没有数据时） |

### 失败响应

**状态码**: 400 Bad Request

**响应体**:

```json
{
  "error": "缺少必填参数: start_time, end_time, account_id, request_path 都是必填的"
}
```

或

```json
{
  "error": "时间格式不正确，正确格式为: YYYY-MM-DD HH:MM:SS"
}
```

或

```json
{
  "error": "时间范围至少需要1分钟"
}
```

或

```json
{
  "error": "时间范围最多为24小时"
}
```

**状态码**: 500 Internal Server Error

**响应体**:

```json
{
  "error": "查询失败: [错误详情]"
}
```

## 实现细节

### 数据来源

该接口从 Elasticsearch 中获取数据，使用的索引为系统操作日志索引（`tgtweb_apioperatelog`）。

### 时间处理

1. **时间格式**：
   - 支持 `YYYY-MM-DD HH:MM:SS` 格式
   - 会验证时间的有效性
   - 确保开始时间早于结束时间

2. **时间范围限制**：
   - 时间范围至少为1分钟
   - 时间范围最多为24小时
   - 这些限制是为了保证查询性能和数据的可视化效果

### 聚合计算逻辑

1. **分钟级聚合**：
   - 使用 Elasticsearch 的 `date_histogram` 聚合，按 `createTime` 字段进行分钟级聚合
   - 聚合结果包含每分钟的文档数量，即访问次数

2. **过滤逻辑**：
   - 只保留访问次数大于等于 `min_count` 的分钟记录
   - 这样可以过滤掉访问频率较低的时间点，突出显示高频访问时段

3. **排序**：
   - 按时间戳升序排序，便于分析时间序列趋势

### 分页处理

1. **内存分页**：
   - 先从 Elasticsearch 获取所有符合条件的聚合结果
   - 然后在内存中进行排序和分页
   - 这种方式适用于结果集不太大的情况（通常分钟级聚合的结果不会太多）

2. **分页参数**：
   - 支持标准的分页参数 `page` 和 `page_size`
   - 默认每页显示10条记录，最大为100条

## 使用示例

### 示例1: 基本查询

**请求**:
```
GET /api/analytics/access-stats/?start_time=2023-04-24%2000:00:00&end_time=2023-04-24%2001:00:00&account_id=admin&request_path=/api/users/
```

**响应**:
```json
{
  "data": [
    {
      "timestamp": "2023-04-24 00:01:00",
      "count": 5
    },
    {
      "timestamp": "2023-04-24 00:02:00",
      "count": 8
    },
    // ... 更多时间点记录
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_count": 35,
    "total_pages": 4
  },
  "summary": {
    "account_id": "admin",
    "request_path": "/api/users/",
    "start_time": "2023-04-24 00:00:00",
    "end_time": "2023-04-24 01:00:00",
    "min_count": 1,
    "total_minutes": 35
  }
}
```

### 示例2: 设置最小访问次数

**请求**:
```
GET /api/analytics/access-stats/?start_time=2023-04-24%2000:00:00&end_time=2023-04-24%2001:00:00&account_id=admin&request_path=/api/users/&min_count=5
```

**响应**:
```json
{
  "data": [
    {
      "timestamp": "2023-04-24 00:01:00",
      "count": 5
    },
    {
      "timestamp": "2023-04-24 00:02:00",
      "count": 8
    },
    {
      "timestamp": "2023-04-24 00:03:00",
      "count": 12
    },
    // ... 更多时间点记录
  ],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total_count": 20,
    "total_pages": 2
  },
  "summary": {
    "account_id": "admin",
    "request_path": "/api/users/",
    "start_time": "2023-04-24 00:00:00",
    "end_time": "2023-04-24 01:00:00",
    "min_count": 5,
    "total_minutes": 20
  }
}
```

### 示例3: 分页查询

**请求**:
```
GET /api/analytics/access-stats/?start_time=2023-04-24%2000:00:00&end_time=2023-04-24%2001:00:00&account_id=admin&request_path=/api/users/&page=2&page_size=5
```

**响应**:
```json
{
  "data": [
    {
      "timestamp": "2023-04-24 00:06:00",
      "count": 7
    },
    {
      "timestamp": "2023-04-24 00:07:00",
      "count": 9
    },
    // ... 更多时间点记录
  ],
  "pagination": {
    "page": 2,
    "page_size": 5,
    "total_count": 35,
    "total_pages": 7
  },
  "summary": {
    "account_id": "admin",
    "request_path": "/api/users/",
    "start_time": "2023-04-24 00:00:00",
    "end_time": "2023-04-24 01:00:00",
    "min_count": 1,
    "total_minutes": 35
  }
}
```

## 相关接口

- `/api/analytics/dashboard/user-behavior-analysis/` - 获取用户行为分析数据
- `/api/analytics/dashboard/feature-operation-analysis/` - 获取功能操作分析数据
- `/api/analytics/dashboard/tps-and-response-time/` - 获取TPS和响应时间数据
- `/api/analytics/log-details/` - 获取日志记录明细

## 注意事项

1. 时间范围至少为1分钟，最多为24小时，这是为了保证查询性能和数据的可视化效果
2. 所有必填参数（start_time、end_time、account_id、request_path）都必须提供，否则会返回错误
3. 时间格式必须为 `YYYY-MM-DD HH:MM:SS`，否则会返回错误
4. 结果按时间戳升序排序，便于分析时间序列趋势
5. 如果某个分钟内没有访问记录，则该分钟不会出现在结果中
6. 设置合适的 `min_count` 值可以过滤掉低频访问，突出显示高频访问时段
7. 该接口主要用于分析用户的访问模式和功能使用频率，可以帮助识别高峰期和用户行为模式
