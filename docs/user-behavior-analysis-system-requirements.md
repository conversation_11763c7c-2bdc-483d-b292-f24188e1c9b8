# 用户行为分析系统需求文档（修订版）

## 1. 项目概述

### 1.1 项目背景
本项目旨在构建一个全面的用户行为分析系统，利用已有的用户操作日志数据进行多维度分析，为业务决策提供数据支持。
系统将基于 Vue3、Python3.8、Elasticsearch7.10 和 MySQL5.7 技术栈，实现前后端分离架构。


## 2. 数据源说明

### 2.1 Elasticsearch 索引结构
api索引名称: tgtweb_apioperatelog 

数据字段说明:
- **cost**: 操作消耗时间（毫秒），长整型
- **ipAddress**: 用户 IP 地址，关键字类型
- **operateType**: 操作类型，关键字类型
- **accountId**: 操作用户 ID，关键字类型
- **result**: 请求结果，长整型
- **createTime**: 请求时间，日期类型
- **requestPath**: 请求 URL，关键字类型
- **className**: api类名，关键字类型
- **methodName**: api类方法名，关键字类型
- **methodParam**:api类入参，关键字类型
- **methodReturn**: api返回数据，关键字类型

oss  索引名称: tgtweb_ossoperatelog

- **cost**: 操作消耗时间（毫秒），长整型
- **ipAddress**: 用户 IP 地址，关键字类型
- **operateType**: 操作类型，关键字类型
- **operator**: 操作用户，关键字类型
- **result**: 请求结果，长整型
- **createTime**: 请求时间，日期类型
- **requestPath**: 请求 URL，关键字类型
- **className**: api类名，关键字类型
- **methodName**: api类方法名，关键字类型
- **methodParam**:api类入参，关键字类型
- **methodReturn**: api返回数据，关键字类型

boss 索引名称: tgtweb_operatelog 
- **costTime**: 操作消耗时间（毫秒），长整型
- **ip**: 用户 IP 地址，关键字类型
- **operateType**: 操作类型，关键字类型
- **operateUserId**: 操作用户 ID，关键字类型
- **operateUserNameAnalyzed**: 操作用户名称
- **operateResult**: 请求结果，长整型
- **requestTime**: 请求时间，日期类型
- **requestUrl**: 请求 URL，关键字类型
- **requestNameAnalyzed**: 请求路径名称
- **requestParam**: 入参，关键字类型
- **requestProjectName**: 项目名称，关键字类型
- **recordType**:记录类型，关键字类型



### 2.2 MySQL 数据库

定时任务统计结果表，统计每个用户每天操作request_url的情况记录

- CREATE TABLE `tgt_api_user_operate_analysis_day` (
-  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
-  `system_type` varchar(20) NOT NULL DEFAULT 'api' COMMENT '系统类型',
-  `request_url` varchar(256) DEFAULT NULL COMMENT '操作路径url',
-  `request_user_id` varchar(64) DEFAULT NULL COMMENT '操作人id',
-  `request_url_name` varchar(255) DEFAULT NULL COMMENT '操作路径名称',
-  `request_type` varchar(32) NOT NULL COMMENT '业务类型',
-  `avg_time` int(10) DEFAULT NULL COMMENT '平均操作时间',
-  `request_count` int(10) DEFAULT NULL COMMENT '操作次数',
-  `day` date NOT NULL COMMENT '统计时间',
-  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
-  PRIMARY KEY (`id`),
-  KEY `url_index` (`request_url`(255)),
-  KEY `user_id_index` (`request_user_id`),
-  KEY `day` (`day`)
- )