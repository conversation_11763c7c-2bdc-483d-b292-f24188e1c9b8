# PowerShell脚本：启动所有服务

# 停止之前可能运行的Python进程
Write-Host "正在检查并停止之前的Python进程..."
$pythonProcesses = Get-Process -Name python -ErrorAction SilentlyContinue
if ($pythonProcesses) {
    $pythonProcesses | ForEach-Object {
        Write-Host "停止Python进程: $($_.Id)"
        Stop-Process -Id $_.Id -Force
    }
}

# 启动Django服务器（在新窗口中）
Write-Host "启动Django服务器..."
Start-Process powershell -ArgumentList "-Command", "python run_server.py; Read-Host 'Press Enter to exit'" -WindowStyle Normal

# 等待几秒钟，确保Django服务器已启动
Write-Host "等待Django服务器启动..."
Start-Sleep -Seconds 5

# 启动简单调度器（在新窗口中）
Write-Host "启动简单调度器..."
Start-Process powershell -ArgumentList "-Command", "python simple_scheduler.py; Read-Host 'Press Enter to exit'" -WindowStyle Normal

Write-Host "所有服务已启动。请不要关闭这些窗口。"
Write-Host "要停止服务，请关闭打开的PowerShell窗口。"
